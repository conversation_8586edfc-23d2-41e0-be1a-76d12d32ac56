"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import Sidebar from '@/components/shared/Sidebar'
import Breadcrumb from '@/components/shared/Breadcrumb'
import { 
  BookOpen, 
  Code2, 
  Palette, 
  Zap, 
  CheckCircle, 
  AlertTriangle,
  Globe,
  Smartphone,
  Monitor,
  Layers,
  Activity,
  Settings,
  FileText,
  ExternalLink,
  Copy,
  ChevronRight,
  Languages
} from 'lucide-react'

export default function FrontendGuidePage() {
  const [activeNavItem, setActiveNavItem] = useState("FrontendGuide")
  const [selectedLanguage, setSelectedLanguage] = useState<'en' | 'zh'>('zh')
  const [copiedCode, setCopiedCode] = useState<string | null>(null)

  const copyToClipboard = (code: string, id: string) => {
    navigator.clipboard.writeText(code)
    setCopiedCode(id)
    setTimeout(() => setCopiedCode(null), 2000)
  }

  const content = {
    zh: {
      title: "前端开发指导",
      subtitle: "交互式开发文档和最佳实践",
      description: "Cape Horn Monitor项目的完整前端开发指导，包含组件系统、工具提示、设计规范和最佳实践。",
      sections: {
        overview: "概述",
        components: "组件系统", 
        tooltips: "工具提示系统",
        guidelines: "开发规范",
        examples: "代码示例",
        testing: "测试指南"
      }
    },
    en: {
      title: "Frontend Development Guide",
      subtitle: "Interactive development documentation and best practices",
      description: "Complete frontend development guide for Cape Horn Monitor project, including component systems, tooltips, design standards, and best practices.",
      sections: {
        overview: "Overview",
        components: "Component System",
        tooltips: "Tooltip System", 
        guidelines: "Development Guidelines",
        examples: "Code Examples",
        testing: "Testing Guide"
      }
    }
  }

  const currentContent = content[selectedLanguage]

  return (
    <div className="flex h-screen bg-background">
      {/* Left Navigation Sidebar */}
      <Sidebar
        activeNavItem={activeNavItem}
        onNavItemChange={setActiveNavItem}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col ml-12 overflow-hidden">
        {/* Top Navigation Bar with Breadcrumb */}
        <Breadcrumb items={[
          { label: "Home", href: "/" },
          { label: "Development Tools", href: "#" },
          { label: "Frontend Guide", isActive: true }
        ]} />

        {/* Page Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <BookOpen className="h-6 w-6 text-primary" />
                </div>
                <div className="flex-1">
                  <h1 className="text-3xl font-bold text-foreground">
                    {currentContent.title}
                  </h1>
                  <p className="text-muted-foreground">
                    {currentContent.subtitle}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant={selectedLanguage === 'zh' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedLanguage('zh')}
                  >
                    中文
                  </Button>
                  <Button
                    variant={selectedLanguage === 'en' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedLanguage('en')}
                  >
                    English
                  </Button>
                  <Badge variant="secondary" className="ml-2">
                    <Languages className="h-3 w-3 mr-1" />
                    Bilingual
                  </Badge>
                </div>
              </div>
              <p className="text-muted-foreground text-lg">
                {currentContent.description}
              </p>
            </div>

            {/* Main Content Tabs */}
            <Tabs defaultValue="overview" className="space-y-6">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="overview">{currentContent.sections.overview}</TabsTrigger>
                <TabsTrigger value="components">{currentContent.sections.components}</TabsTrigger>
                <TabsTrigger value="tooltips">{currentContent.sections.tooltips}</TabsTrigger>
                <TabsTrigger value="guidelines">{currentContent.sections.guidelines}</TabsTrigger>
                <TabsTrigger value="examples">{currentContent.sections.examples}</TabsTrigger>
                <TabsTrigger value="testing">{currentContent.sections.testing}</TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Component System Card */}
                  <Card className="hover:shadow-lg transition-all duration-200 hover:border-primary/50">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Layers className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '组件系统' : 'Component System'}
                      </CardTitle>
                      <CardDescription>
                        {selectedLanguage === 'zh' 
                          ? '基础UI Card系统和MonitorCard组件的完整文档'
                          : 'Complete documentation for Basic UI Card system and MonitorCard components'
                        }
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">
                            {selectedLanguage === 'zh' ? '7个可组合Card组件' : '7 composable Card components'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">
                            {selectedLanguage === 'zh' ? '15+图表类型支持' : '15+ chart types supported'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">
                            {selectedLanguage === 'zh' ? '设计系统集成' : 'Design system integration'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Tooltip System Card */}
                  <Card className="hover:shadow-lg transition-all duration-200 hover:border-primary/50">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '工具提示系统' : 'Tooltip System'}
                      </CardTitle>
                      <CardDescription>
                        {selectedLanguage === 'zh' 
                          ? '交互式图表工具提示的高级实现和配置'
                          : 'Advanced implementation and configuration of interactive chart tooltips'
                        }
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">
                            {selectedLanguage === 'zh' ? '智能定位系统' : 'Smart positioning system'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">
                            {selectedLanguage === 'zh' ? '指标特定格式化' : 'Metric-specific formatting'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">
                            {selectedLanguage === 'zh' ? '跨浏览器兼容' : 'Cross-browser compatibility'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Development Guidelines Card */}
                  <Card className="hover:shadow-lg transition-all duration-200 hover:border-primary/50">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '开发规范' : 'Development Guidelines'}
                      </CardTitle>
                      <CardDescription>
                        {selectedLanguage === 'zh' 
                          ? '代码质量、测试和最佳实践的标准化指南'
                          : 'Standardized guidelines for code quality, testing, and best practices'
                        }
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">
                            {selectedLanguage === 'zh' ? 'TypeScript最佳实践' : 'TypeScript best practices'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">
                            {selectedLanguage === 'zh' ? '无障碍性指南' : 'Accessibility guidelines'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">
                            {selectedLanguage === 'zh' ? '性能优化策略' : 'Performance optimization'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-primary">3</div>
                      <div className="text-sm text-muted-foreground">
                        {selectedLanguage === 'zh' ? '主要组件系统' : 'Major Component Systems'}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-primary">15+</div>
                      <div className="text-sm text-muted-foreground">
                        {selectedLanguage === 'zh' ? '图表类型' : 'Chart Types'}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-primary">2</div>
                      <div className="text-sm text-muted-foreground">
                        {selectedLanguage === 'zh' ? '语言版本' : 'Language Versions'}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-primary">100%</div>
                      <div className="text-sm text-muted-foreground">
                        {selectedLanguage === 'zh' ? '测试覆盖率' : 'Test Coverage'}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Components Tab */}
              <TabsContent value="components" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Basic UI Card System */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Layers className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '基础UI Card系统' : 'Basic UI Card System'}
                      </CardTitle>
                      <CardDescription>
                        {selectedLanguage === 'zh' 
                          ? '7个可组合组件的基础卡片系统'
                          : 'Foundational card system with 7 composable components'
                        }
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <h4 className="font-medium">
                          {selectedLanguage === 'zh' ? '组件列表' : 'Component List'}
                        </h4>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>• Card</div>
                          <div>• CardHeader</div>
                          <div>• CardTitle</div>
                          <div>• CardDescription</div>
                          <div>• CardContent</div>
                          <div>• CardFooter</div>
                          <div>• CardAction</div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-medium">
                          {selectedLanguage === 'zh' ? '主要特性' : 'Key Features'}
                        </h4>
                        <ul className="text-sm space-y-1 text-muted-foreground">
                          <li>• {selectedLanguage === 'zh' ? '设计系统集成' : 'Design system integration'}</li>
                          <li>• {selectedLanguage === 'zh' ? '响应式设计' : 'Responsive design'}</li>
                          <li>• {selectedLanguage === 'zh' ? '无障碍性支持' : 'Accessibility support'}</li>
                          <li>• {selectedLanguage === 'zh' ? '主题感知' : 'Theme awareness'}</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>

                  {/* MonitorCard Component */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Monitor className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? 'MonitorCard组件' : 'MonitorCard Component'}
                      </CardTitle>
                      <CardDescription>
                        {selectedLanguage === 'zh' 
                          ? '高级监控数据可视化组件'
                          : 'Advanced monitoring data visualization component'
                        }
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <h4 className="font-medium">
                          {selectedLanguage === 'zh' ? '图表类型' : 'Chart Types'}
                        </h4>
                        <div className="grid grid-cols-2 gap-1 text-sm">
                          <div>• Area</div>
                          <div>• Line</div>
                          <div>• Bar</div>
                          <div>• Scatter</div>
                          <div>• Composed</div>
                          <div>• Multi-line</div>
                          <div>• Bubble</div>
                          <div>• Gradient</div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-medium">
                          {selectedLanguage === 'zh' ? '健康指标' : 'Health Indicators'}
                        </h4>
                        <ul className="text-sm space-y-1 text-muted-foreground">
                          <li>• NHI (Network Health Indicator)</li>
                          <li>• THI (Transaction Health Indicator)</li>
                          <li>• {selectedLanguage === 'zh' ? '实时数据更新' : 'Real-time data updates'}</li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Tooltips Tab */}
              <TabsContent value="tooltips" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Smart Positioning */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '智能定位' : 'Smart Positioning'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '突破视图框限制' : 'Escape ViewBox'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '碰撞检测' : 'Collision Detection'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? 'Z轴管理' : 'Z-Index Management'}
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Design Integration */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Palette className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '设计集成' : 'Design Integration'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? 'CSS自定义属性' : 'CSS Custom Properties'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '主题感知' : 'Theme Awareness'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '颜色协调' : 'Color Coordination'}
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Browser Support */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Globe className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '浏览器支持' : 'Browser Support'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">Chrome 90+</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">Firefox 88+</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">Safari 14+</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '移动端支持' : 'Mobile Support'}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Tooltip Configuration Example */}
                <Card>
                  <CardHeader>
                    <CardTitle>
                      {selectedLanguage === 'zh' ? '工具提示配置示例' : 'Tooltip Configuration Example'}
                    </CardTitle>
                    <CardDescription>
                      {selectedLanguage === 'zh'
                        ? '基本工具提示设置和高级配置选项'
                        : 'Basic tooltip setup and advanced configuration options'
                      }
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="relative">
                        <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                          <code>{`import { Tooltip } from 'recharts'
import { getTooltipConfig } from '@/components/shared/MonitorCard'

<AreaChart data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
  <Area dataKey="inMbps" stroke="var(--chart-network-inbound)" />
</AreaChart>`}</code>
                        </pre>
                        <Button
                          size="sm"
                          variant="outline"
                          className="absolute top-2 right-2"
                          onClick={() => copyToClipboard(`import { Tooltip } from 'recharts'
import { getTooltipConfig } from '@/components/shared/MonitorCard'

<AreaChart data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
  <Area dataKey="inMbps" stroke="var(--chart-network-inbound)" />
</AreaChart>`, 'tooltip-basic')}
                        >
                          {copiedCode === 'tooltip-basic' ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Guidelines Tab */}
              <TabsContent value="guidelines" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Code Quality */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Code2 className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '代码质量' : 'Code Quality'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? 'TypeScript严格模式' : 'TypeScript strict mode'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? 'ESLint规则遵循' : 'ESLint rules compliance'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '代码格式化' : 'Code formatting'}
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Accessibility */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Smartphone className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '无障碍性' : 'Accessibility'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? 'WCAG 2.1 AA标准' : 'WCAG 2.1 AA compliance'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '键盘导航支持' : 'Keyboard navigation'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '屏幕阅读器兼容' : 'Screen reader support'}
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Performance */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '性能优化' : 'Performance'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '懒加载组件' : 'Lazy loading'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '内存优化' : 'Memory optimization'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">
                          {selectedLanguage === 'zh' ? '包大小控制' : 'Bundle size control'}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Best Practices */}
                <Card>
                  <CardHeader>
                    <CardTitle>
                      {selectedLanguage === 'zh' ? '最佳实践指南' : 'Best Practices Guide'}
                    </CardTitle>
                    <CardDescription>
                      {selectedLanguage === 'zh'
                        ? '前端开发的核心原则和推荐做法'
                        : 'Core principles and recommended practices for frontend development'
                      }
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <h4 className="font-semibold mb-2 text-green-600 flex items-center gap-2">
                          <CheckCircle className="h-4 w-4" />
                          {selectedLanguage === 'zh' ? '推荐做法' : 'Do'}
                        </h4>
                        <ul className="space-y-1 text-sm text-muted-foreground">
                          <li>• {selectedLanguage === 'zh' ? '使用设计令牌' : 'Use design tokens'}</li>
                          <li>• {selectedLanguage === 'zh' ? '遵循组件模式' : 'Follow component patterns'}</li>
                          <li>• {selectedLanguage === 'zh' ? '编写测试用例' : 'Write test cases'}</li>
                          <li>• {selectedLanguage === 'zh' ? '文档化API' : 'Document APIs'}</li>
                          <li>• {selectedLanguage === 'zh' ? '优化性能' : 'Optimize performance'}</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-red-600 flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4" />
                          {selectedLanguage === 'zh' ? '避免做法' : "Don't"}
                        </h4>
                        <ul className="space-y-1 text-sm text-muted-foreground">
                          <li>• {selectedLanguage === 'zh' ? '硬编码样式值' : 'Hardcode style values'}</li>
                          <li>• {selectedLanguage === 'zh' ? '忽略无障碍性' : 'Ignore accessibility'}</li>
                          <li>• {selectedLanguage === 'zh' ? '跳过错误处理' : 'Skip error handling'}</li>
                          <li>• {selectedLanguage === 'zh' ? '过度优化' : 'Over-optimize prematurely'}</li>
                          <li>• {selectedLanguage === 'zh' ? '忽略浏览器兼容' : 'Ignore browser compatibility'}</li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-blue-600 flex items-center gap-2">
                          <Zap className="h-4 w-4" />
                          {selectedLanguage === 'zh' ? '优化建议' : 'Tips'}
                        </h4>
                        <ul className="space-y-1 text-sm text-muted-foreground">
                          <li>• {selectedLanguage === 'zh' ? '使用React DevTools' : 'Use React DevTools'}</li>
                          <li>• {selectedLanguage === 'zh' ? '监控包大小' : 'Monitor bundle size'}</li>
                          <li>• {selectedLanguage === 'zh' ? '定期重构代码' : 'Refactor regularly'}</li>
                          <li>• {selectedLanguage === 'zh' ? '保持依赖更新' : 'Keep dependencies updated'}</li>
                          <li>• {selectedLanguage === 'zh' ? '使用TypeScript' : 'Leverage TypeScript'}</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Examples Tab */}
              <TabsContent value="examples" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Basic Card Example */}
                  <Card>
                    <CardHeader>
                      <CardTitle>
                        {selectedLanguage === 'zh' ? '基础卡片示例' : 'Basic Card Example'}
                      </CardTitle>
                      <CardDescription>
                        {selectedLanguage === 'zh'
                          ? '使用基础UI Card组件的简单示例'
                          : 'Simple example using Basic UI Card components'
                        }
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="relative">
                        <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                          <code>{`import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'

function BasicCard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
      </CardHeader>
      <CardContent>
        <p>Card content goes here.</p>
      </CardContent>
    </Card>
  )
}`}</code>
                        </pre>
                        <Button
                          size="sm"
                          variant="outline"
                          className="absolute top-2 right-2"
                          onClick={() => copyToClipboard(`import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'

function BasicCard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
      </CardHeader>
      <CardContent>
        <p>Card content goes here.</p>
      </CardContent>
    </Card>
  )
}`, 'basic-card')}
                        >
                          {copiedCode === 'basic-card' ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Monitor Card Example */}
                  <Card>
                    <CardHeader>
                      <CardTitle>
                        {selectedLanguage === 'zh' ? '监控卡片示例' : 'Monitor Card Example'}
                      </CardTitle>
                      <CardDescription>
                        {selectedLanguage === 'zh'
                          ? '带图表和健康指标的监控卡片'
                          : 'Monitor card with charts and health indicators'
                        }
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="relative">
                        <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                          <code>{`import MonitorCard from '@/components/shared/MonitorCard'

const monitor = {
  id: "network-1",
  name: "Network Monitor",
  status: "active",
  type: "network",
  showMetrics: true,
  chartType: "area"
}

<MonitorCard
  monitor={monitor}
  onClick={(m) => console.log('Clicked:', m.name)}
/>`}</code>
                        </pre>
                        <Button
                          size="sm"
                          variant="outline"
                          className="absolute top-2 right-2"
                          onClick={() => copyToClipboard(`import MonitorCard from '@/components/shared/MonitorCard'

const monitor = {
  id: "network-1",
  name: "Network Monitor",
  status: "active",
  type: "network",
  showMetrics: true,
  chartType: "area"
}

<MonitorCard
  monitor={monitor}
  onClick={(m) => console.log('Clicked:', m.name)}
/>`, 'monitor-card')}
                        >
                          {copiedCode === 'monitor-card' ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Testing Tab */}
              <TabsContent value="testing" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Unit Testing */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Settings className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '单元测试' : 'Unit Testing'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <h4 className="font-medium">
                          {selectedLanguage === 'zh' ? '测试要求' : 'Testing Requirements'}
                        </h4>
                        <ul className="text-sm space-y-1 text-muted-foreground">
                          <li>• {selectedLanguage === 'zh' ? '组件渲染测试' : 'Component rendering tests'}</li>
                          <li>• {selectedLanguage === 'zh' ? '属性处理验证' : 'Props handling verification'}</li>
                          <li>• {selectedLanguage === 'zh' ? '交互行为测试' : 'Interaction behavior tests'}</li>
                          <li>• {selectedLanguage === 'zh' ? '无障碍性测试' : 'Accessibility testing'}</li>
                        </ul>
                      </div>
                      <div className="relative">
                        <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
                          <code>{`test('renders tooltip with metrics', () => {
  render(<CustomTooltip active={true} payload={mockPayload} />)
  expect(screen.getByText('Inbound Traffic')).toBeInTheDocument()
})`}</code>
                        </pre>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Performance Testing */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5 text-primary" />
                        {selectedLanguage === 'zh' ? '性能测试' : 'Performance Testing'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <h4 className="font-medium">
                          {selectedLanguage === 'zh' ? '性能指标' : 'Performance Metrics'}
                        </h4>
                        <ul className="text-sm space-y-1 text-muted-foreground">
                          <li>• {selectedLanguage === 'zh' ? '渲染时间 <16ms' : 'Render time <16ms'}</li>
                          <li>• {selectedLanguage === 'zh' ? '内存使用 <1MB' : 'Memory usage <1MB'}</li>
                          <li>• {selectedLanguage === 'zh' ? '包大小优化' : 'Bundle size optimization'}</li>
                          <li>• {selectedLanguage === 'zh' ? '交互响应性' : 'Interaction responsiveness'}</li>
                        </ul>
                      </div>
                      <div className="relative">
                        <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
                          <code>{`console.time('tooltip-render')
// Tooltip rendering code
console.timeEnd('tooltip-render')`}</code>
                        </pre>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}
