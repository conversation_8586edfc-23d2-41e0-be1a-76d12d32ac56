# MonitorCard Component System

## Component Overview

- **Component Name**: MonitorCard
- **File Path**: `components/shared/MonitorCard.tsx`
- **Purpose**: Advanced monitoring data visualization card with chart integration, health indicators, and intelligent icon selection
- **Complexity Level**: High
- **Dependencies**: 
  - `@/components/ui/card` (Basic UI Card System)
  - `recharts` (Chart library)
  - `lucide-react` (Icon library)
  - `@/lib/monitor-data` (Data generation utilities)
  - `@/lib/chart-colors` (Color system)

## Props and Configuration

### MonitorCardData Interface
```typescript
export interface MonitorCardData {
  // Basic Properties
  id: string
  name: string
  status: string
  lastUpdated?: string
  description?: string
  route?: string
  
  // Visual Configuration
  iconColor?: string
  statusColor?: string
  iconType?: string
  
  // Enhanced Display Features
  type?: 'network' | 'transaction'
  showMetrics?: boolean
  chartType?: ChartType
  dataPattern?: DataPattern
  
  // Advanced Customization
  chartColors?: {
    primary?: string
    secondary?: string
    accent?: string
    gradient?: string[]
  }
  chartStyle?: {
    strokeWidth?: number
    opacity?: number
    animation?: boolean
    glow?: boolean
  }
}
```

### Chart Types
- **line**: Basic line chart for trend visualization
- **area**: Area chart with gradient fills
- **bar**: Bar chart for discrete data points
- **scatter**: Scatter plot for correlation analysis
- **composed**: Multi-metric overlay charts
- **gradient-area**: Enhanced area chart with multi-color gradients
- **multi-line**: Multiple line series overlay
- **stacked-bar**: Stacked bar chart with segments
- **bubble**: Dual scatter series visualization
- **radial**: Radial gradient charts
- **waterfall**: Sequential bar progression
- **pulse-wave**: Enhanced composed charts with effects

### Data Patterns
- **normal**: Standard data distribution
- **spike**: Sudden peak in the middle
- **step**: Step function with level changes
- **oscillating**: Sine wave pattern
- **declining**: Gradual decline trend
- **recovering**: Recovery from low point
- **sawtooth**: Triangular wave pattern
- **exponential**: Exponential growth curve
- **logarithmic**: Logarithmic leveling curve
- **random-walk**: Random noise pattern
- **heartbeat**: Pulse-like pattern
- **cascade**: Step-down progression
- **pulse-burst**: Burst pattern with intervals

### Health Indicators
- **NHI (Network Health Indicator)**: For network-type monitors
- **THI (Transaction Health Indicator)**: For transaction-type monitors
- **Color Coding**: Green (healthy), Yellow (warning), Red (critical)

## Visual Design Specifications

### Dimensions
- **Fixed Size**: `monitor-card-size` (20rem × 15rem / 320px × 240px)
- **Responsive**: Maintains aspect ratio across screen sizes
- **Content Areas**: Header, chart, health indicator, test badge

### Colors (Design Tokens)
- **Background**: Inherits from Card component (`var(--card)`)
- **Chart Colors**: Dynamic based on monitor type and configuration
- **Health Indicators**: 
  - Green: `#10b981` (healthy)
  - Yellow: `#f59e0b` (warning)
  - Red: `#ef4444` (critical)
- **Test Badge**: Amber theme (`border-amber-200 bg-amber-50/20`)

### Typography
- **Monitor Name**: `font-medium text-foreground` with fade effect
- **Health Indicator**: `text-xs font-medium uppercase tracking-wide`
- **Test Badge**: `text-[10px] font-medium uppercase tracking-wide opacity-60`

### Spacing
- **Enhanced Mode**: `p-3` (12px padding)
- **Simple Mode**: `p-4` (16px padding)
- **Icon Container**: `w-12 h-12` (48px × 48px)
- **Chart Area**: Responsive container with proper margins

### Interactive Elements
- **Hover Effects**: Enhanced shadow and border color changes
- **Click Handling**: Full card clickable area
- **Transitions**: `transition-all duration-200`

## Chart System Integration

### Chart Color System
Uses design token-based color system from `@/lib/chart-colors`:

```typescript
// Network Monitor Colors
{
  primary: '--chart-network-inbound',
  secondary: '--chart-network-outbound', 
  accent: '--chart-network-latency'
}

// Transaction Monitor Colors
{
  primary: '--chart-transaction-requests',
  secondary: '--chart-transaction-success',
  accent: '--chart-transaction-response'
}
```

### Metric Mapping
#### Network Metrics
- `inMbps`: Inbound Traffic (Mbps)
- `outMbps`: Outbound Traffic (Mbps)
- `rtt`: Round Trip Time (ms)
- `loss`: Packet Loss (%)
- `retrans`: Retransmission (%)

#### Transaction Metrics
- `req`: Requests (/min)
- `successRate`: Success Rate (%)
- `respP95`: Response Time P95 (ms)
- `errorRate`: Error Rate (%)

### Health Calculation
```typescript
// Network Health Indicator (NHI)
const nhi = calculateNHI(dataPoints)

// Transaction Health Indicator (THI)
const thi = calculateTHI(dataPoints)
```

## Icon System

### Intelligent Icon Selection
Automatic icon selection based on monitor name patterns:

- **Payment/Financial**: `CreditCard` (visa, payment, card)
- **Database**: `Database` (database, db, sql)
- **API/Services**: `Server` (api, gateway, service)
- **Load Balancing**: `Zap` (load, balancer, traffic)
- **Security**: `Shield` (auth, security, login)
- **Network**: `Network` (network, connectivity)
- **Storage**: `HardDrive` (storage, disk, volume)
- **Monitoring**: `Activity` (monitor, metrics, stats)
- **Cloud**: `Cloud` (cloud, aws, azure)
- **CPU**: `Cpu` (cpu, processor, compute)
- **Default**: `MonitorIcon`

### Icon Styling
- **Unified Design**: Diagonal texture pattern (`icon-texture-diagonal`)
- **Colors**: `bg-slate-50 text-slate-700 border border-slate-200`
- **Size**: `h-6 w-6` within `w-12 h-12` container

## Usage Examples

### Basic Monitor Card
```typescript
import MonitorCard, { MonitorCardData } from "@/components/shared/MonitorCard"

const basicMonitor: MonitorCardData = {
  id: "basic-monitor",
  name: "Basic Service Monitor",
  status: "active",
  lastUpdated: "2 minutes ago",
  iconColor: "blue",
  statusColor: "green"
}

function BasicExample() {
  return (
    <MonitorCard 
      monitor={basicMonitor}
      onClick={(monitor) => console.log('Clicked:', monitor.name)}
    />
  )
}
```

### Enhanced Monitor with Metrics
```typescript
const enhancedMonitor: MonitorCardData = {
  id: "visa-service",
  name: "VISA Service",
  status: "active",
  type: "network",
  showMetrics: true,
  iconType: "credit-card",
  chartType: "area",
  dataPattern: "normal",
  statusColor: "green"
}

function EnhancedExample() {
  return (
    <MonitorCard 
      monitor={enhancedMonitor}
      onClick={(monitor) => navigateToDetails(monitor.route)}
    />
  )
}
```

### Advanced Custom Configuration
```typescript
const advancedMonitor: MonitorCardData = {
  id: "custom-monitor",
  name: "[TEST] Payment Gateway",
  type: "transaction",
  showMetrics: true,
  chartType: "gradient-area",
  dataPattern: "sawtooth",
  chartColors: {
    primary: "#8b5cf6",
    secondary: "#06b6d4",
    accent: "#10b981"
  },
  chartStyle: {
    strokeWidth: 3,
    opacity: 0.8,
    glow: true
  },
  iconType: "zap",
  statusColor: "green"
}
```

## Responsive Behavior

### Container Adaptation
- Fixed aspect ratio maintained across screen sizes
- Chart responsiveness handled by `ResponsiveContainer`
- Grid layout compatibility with `monitor-grid-responsive`

### Mobile Considerations
- Touch-friendly interaction areas
- Readable text at small sizes
- Appropriate chart scaling

## Accessibility

### ARIA Support
- Proper semantic structure
- Screen reader friendly chart descriptions
- Keyboard navigation support

### Visual Accessibility
- High contrast health indicators
- Clear visual hierarchy
- Readable typography at all sizes

## Performance Considerations

### Data Generation
- Client-side rendering with `useEffect` and `useState`
- Memoized calculations with `useMemo`
- Efficient data pattern generation

### Chart Rendering
- Lazy loading with client-side checks
- Optimized Recharts configuration
- Minimal re-renders through proper dependencies

### Memory Management
- Proper cleanup of chart instances
- Efficient data structure usage
- Optimized color calculations

## Testing Guidelines

### Unit Tests
- Component rendering with different configurations
- Health indicator calculations
- Icon selection logic
- Chart type rendering

### Visual Testing
- Chart appearance across different data patterns
- Health indicator color accuracy
- Responsive behavior verification

### Integration Testing
- Click handling functionality
- Data generation accuracy
- Chart interaction testing

## Chart Implementation Details

### Chart Type Rendering
```typescript
// Area Chart Example
<AreaChart data={data} syncId="main">
  <defs>
    <linearGradient id={`gradientArea-${monitor.id}`} x1="0" y1="0" x2="0" y2="1">
      <stop offset="5%" stopColor={colors.primary} stopOpacity={0.8}/>
      <stop offset="95%" stopColor={colors.primary} stopOpacity={0.1}/>
    </linearGradient>
  </defs>
  <Area
    type="monotone"
    dataKey="inMbps"
    stroke={colors.primary}
    fill={`url(#gradientArea-${monitor.id})`}
    strokeWidth={2}
    dot={false}
  />
  <Tooltip content={<CustomTooltip />} />
</AreaChart>
```

### Custom Tooltip Implementation
```typescript
function CustomTooltip({ active, payload, label }: any) {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border border-border corner-sm p-2 shadow-md">
        <p className="text-xs text-muted-foreground">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-xs" style={{ color: entry.color }}>
            {`${entry.name}: ${entry.value}${getMetricInfo(entry.dataKey).unit}`}
          </p>
        ))}
      </div>
    )
  }
  return null
}
```

### Test Card Detection
```typescript
const isTestCard = monitor.id.startsWith('test-') || monitor.name.includes('[TEST]')
```

## State Management

### Client-Side Rendering
```typescript
const [isClient, setIsClient] = useState(false)

useEffect(() => {
  setIsClient(true)
}, [])

// Prevents hydration mismatches
if (!isClient) return <LoadingState />
```

### Data Memoization
```typescript
const data = useMemo(() => {
  if (!isClient || !monitor.showMetrics || !monitor.type) return []
  if (monitor.dataPattern) {
    return generateMiniCardDataWithPattern(monitor.type, monitor.dataPattern)
  }
  return generateMiniCardData(monitor.type)
}, [isClient, monitor.showMetrics, monitor.type, monitor.dataPattern])
```

## Migration and Compatibility

### Version History
- v1.0: Basic monitor card implementation
- v2.0: Enhanced metrics and chart integration
- v3.0: Advanced chart types and health indicators
- Current: Full design token integration

### Breaking Changes
- Chart color system migration to design tokens
- Enhanced interface with new optional properties
- Icon system unification

### Legacy Support
- Maintains backward compatibility with basic monitor data
- Graceful fallback to simple card when metrics unavailable
- Progressive enhancement approach

---

**Related Components**: [Basic UI Card](./basic-ui-card-system.md) | [Chart Color System](../standards/chart-color-system.md) | [Monitor Data System](../standards/monitor-data-patterns.md)
