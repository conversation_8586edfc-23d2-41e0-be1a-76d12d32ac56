# Basic UI Card System

## Component Overview

- **Component Name**: Basic UI Card System
- **File Path**: `components/ui/card.tsx`
- **Purpose**: Foundational card component system providing a complete set of composable card elements for building consistent UI layouts
- **Complexity Level**: Medium
- **Dependencies**: 
  - `@/lib/utils` (cn utility for className merging)
  - React (ComponentProps type)

## Props and Configuration

### Card Component Interface
```typescript
interface CardProps extends React.ComponentProps<"div"> {
  'data-clickable'?: string | boolean;
}
```

### Component Family
The system exports 7 interconnected components:

1. **Card** - Root container component
2. **CardHeader** - Header section with grid layout support
3. **CardTitle** - Primary heading element
4. **CardDescription** - Secondary descriptive text
5. **CardAction** - Action button/element container
6. **CardContent** - Main content area
7. **CardFooter** - Footer section with flex layout

### Required Props
- None - all components work with default configurations

### Optional Props
- **className**: Additional CSS classes for customization
- **data-clickable**: Explicit clickable state control (string "true" or boolean true)
- **onClick**: Click handler (automatically enables cursor-pointer)
- All standard HTML div props are supported via spread operator

## Visual Design Specifications

### Dimensions
- **Default Width**: Flexible (100% of container)
- **Default Height**: Auto-sizing based on content
- **Sizing Behavior**: Responsive and container-aware

### Colors (Design Tokens)
- **Background**: `var(--card)` (#ffffff light, #1e293b dark)
- **Text Color**: `var(--card-foreground)` (#4b5563 light, #f1f5f9 dark)
- **Border**: `var(--border)` (inherited from design system)
- **Muted Text**: `var(--muted-foreground)` (for descriptions)

### Typography
- **Card Title**: `font-semibold leading-none`
- **Card Description**: `text-sm text-muted-foreground`
- **Content**: Inherits from parent/design system

### Spacing
- **Card Padding**: `py-6` (24px vertical)
- **Internal Gap**: `gap-6` (24px between child elements)
- **Header Padding**: `px-6` (24px horizontal)
- **Content Padding**: `px-6` (24px horizontal)
- **Footer Padding**: `px-6` (24px horizontal)
- **Header Gap**: `gap-1.5` (6px between title and description)

### Border Radius
- **Corner Radius**: `corner-sm` (8px / 0.5rem)
- **System**: Uses new corner radius design tokens

### Shadows
- **Default Shadow**: `shadow-sm` (subtle elevation)
- **Hover Shadow**: Enhanced via hover states (implementation dependent)

## Interactive States

### Default State
- Clean card appearance with subtle border and shadow
- Flex column layout with consistent spacing
- Non-interactive cursor (default)

### Hover State
- Automatic cursor-pointer when clickable
- Enhanced shadow effects (when implemented in usage)
- Smooth transitions (when implemented in usage)

### Focus State
- Follows design system focus ring patterns
- Keyboard accessible when interactive
- ARIA-compliant focus management

### Active State
- Visual feedback for pressed state (implementation dependent)
- Maintains accessibility standards

### Disabled State
- Not implemented at component level
- Should be handled by parent components

## Responsive Behavior

### Breakpoint Behavior
- Fully responsive design
- Adapts to container width
- No fixed breakpoints at component level

### Container Queries
- **CardHeader**: Uses `@container/card-header` for responsive layout
- Supports container-based responsive design patterns

### Mobile Considerations
- Touch-friendly interaction areas
- Appropriate spacing for mobile interfaces
- Flexible layout adaptation

### Grid Layout
- **CardHeader**: Advanced CSS Grid layout
  - `grid-rows-[auto_auto]` for title/description stacking
  - `grid-cols-[1fr_auto]` when CardAction is present
  - Automatic layout adjustment based on content

## Accessibility

### ARIA Labels
- **data-slot attributes**: Semantic identification for each component
- Proper heading hierarchy support
- Screen reader friendly structure

### Keyboard Navigation
- Tab navigation support when interactive
- Focus management for clickable cards
- Keyboard event handling (when onClick is provided)

### Screen Reader Support
- Semantic HTML structure
- Proper heading levels
- Descriptive content organization

### Color Contrast
- Meets WCAG AA standards
- High contrast in both light and dark modes
- Proper text-to-background contrast ratios

### Focus Management
- Visible focus indicators
- Logical tab order
- Focus trap support (when needed)

## Usage Examples

### Basic Usage
```typescript
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"

function BasicCard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>Card description text</CardDescription>
      </CardHeader>
      <CardContent>
        <p>Main card content goes here.</p>
      </CardContent>
    </Card>
  )
}
```

### Advanced Usage with Actions
```typescript
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, CardAction } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Settings } from "lucide-react"

function AdvancedCard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Advanced Card</CardTitle>
        <CardDescription>Card with action button</CardDescription>
        <CardAction>
          <Button variant="ghost" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </CardAction>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p>Content with structured layout</p>
        </div>
      </CardContent>
      <CardFooter className="border-t">
        <Button className="w-full">Primary Action</Button>
      </CardFooter>
    </Card>
  )
}
```

### Clickable Card Pattern
```typescript
function ClickableCard() {
  const handleClick = () => {
    console.log("Card clicked")
  }

  return (
    <Card onClick={handleClick}> {/* Automatically gets cursor-pointer */}
      <CardHeader>
        <CardTitle>Clickable Card</CardTitle>
        <CardDescription>This entire card is clickable</CardDescription>
      </CardHeader>
      <CardContent>
        <p>Click anywhere on this card to trigger the action.</p>
      </CardContent>
    </Card>
  )
}
```

## Code Examples

### HTML Structure
```html
<div data-slot="card" class="bg-card text-card-foreground flex flex-col gap-6 corner-sm border py-6 shadow-sm">
  <div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6">
    <div data-slot="card-title" class="leading-none font-semibold">Title</div>
    <div data-slot="card-description" class="text-muted-foreground text-sm">Description</div>
  </div>
  <div data-slot="card-content" class="px-6">Content</div>
</div>
```

### CSS Styling (Tailwind Classes)
```css
/* Card Root */
.card-root {
  @apply bg-card text-card-foreground flex flex-col gap-6 corner-sm border py-6 shadow-sm;
}

/* Clickable State */
.card-clickable {
  @apply cursor-pointer;
}

/* Header Layout */
.card-header {
  @apply grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6;
}

/* With Action Button */
.card-header-with-action {
  @apply has-data-[slot=card-action]:grid-cols-[1fr_auto];
}
```

## Design System Integration

### Design Tokens Used
- `--card`: Background color
- `--card-foreground`: Text color
- `--muted-foreground`: Secondary text color
- `--border`: Border color
- `--corner-sm`: Border radius (8px)

### Theme Support
- Full light/dark mode compatibility
- Automatic color scheme adaptation
- Consistent with design system tokens

### Brand Consistency
- Follows established spacing scale
- Uses consistent typography hierarchy
- Maintains brand color palette

### Component Relationships
- **Base for**: MonitorCard, VisaPreview cards
- **Works with**: Button, Badge, Icon components
- **Layout**: Integrates with Grid and Flex systems

## Testing Guidelines

### Unit Tests
- Component rendering tests
- Props handling verification
- Clickable state detection
- Accessibility compliance

### Visual Testing
- Screenshot tests for all variants
- Cross-browser visual consistency
- Responsive layout verification

### Accessibility Testing
- Screen reader compatibility
- Keyboard navigation testing
- Color contrast validation
- Focus management verification

## Performance Considerations

### Bundle Size
- Minimal footprint (< 2KB gzipped)
- Tree-shakeable exports
- No external dependencies

### Rendering Performance
- Optimized CSS Grid layouts
- Efficient className merging
- Minimal re-renders

### Best Practices
- Use semantic HTML structure
- Leverage CSS Grid for complex layouts
- Implement proper focus management
- Follow accessibility guidelines

## Migration and Compatibility

### Breaking Changes
- None in current version
- Stable API since implementation

### Version History
- v1.0: Initial implementation with 7-component system
- Current: Enhanced accessibility and container query support

### Deprecation Notices
- None currently
- Legacy rounded-* classes being phased out in favor of corner-* system

---

**Related Components**: [MonitorCard](./monitor-card-system.md) | [Design System](../standards/design-tokens.md)
