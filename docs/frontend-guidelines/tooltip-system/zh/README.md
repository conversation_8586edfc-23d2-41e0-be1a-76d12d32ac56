# 工具提示系统文档

## 概述

本综合指南涵盖了Cape Horn Monitor项目中图表数据可视化的工具提示系统实现。该系统为MonitorCard组件提供交互式工具提示，具有高级定位、设计系统集成和跨浏览器兼容性。

## 文档结构

### 核心文档
- [**组件API参考**](./component-api-reference.md) - 完整的TypeScript接口和属性
- [**实现指南**](./implementation-guide.md) - 分步实现说明
- [**集成模式**](./integration-patterns.md) - 工具提示与其他系统的集成方式
- [**使用示例**](./usage-examples.md) - 完整的工作代码示例

### 开发者资源
- [**快速参考**](./quick-reference.md) - 常见任务和代码片段
- [**故障排除指南**](./troubleshooting-guide.md) - 常见问题和解决方案
- [**性能指南**](./performance-guide.md) - 优化最佳实践
- [**测试指南**](./testing-guide.md) - 测试方法和要求

### 质量保证
- [**无障碍指南**](./accessibility-guide.md) - WCAG合规性和最佳实践
- [**跨浏览器兼容性**](./cross-browser-compatibility.md) - 浏览器支持和测试
- [**移动端响应式**](./mobile-responsiveness.md) - 触摸和移动端考虑
- [**验证清单**](./validation-checklist.md) - 实现验证

## 主要特性

### 🎯 智能定位
- **突破视图框**: 工具提示可以扩展到图表边界之外
- **碰撞检测**: 自动定位以避免内容阻挡
- **Z轴管理**: 与其他UI元素的正确分层
- **偏移控制**: 可配置的悬停点间距

### 🎨 设计系统集成
- **CSS自定义属性**: 使用设计令牌进行主题化
- **主题感知**: 自动适应明暗模式
- **颜色协调**: 指标颜色与图表元素匹配
- **排版一致性**: 遵循既定的文本层次结构

### 📊 图表类型支持
- **15+图表类型**: 面积图、折线图、柱状图、散点图、组合图等
- **指标特定**: 网络与交易数据的不同工具提示
- **动态内容**: 适应可用数据和图表配置
- **交互式光标**: 图表特定的光标样式

### 🌐 国际化就绪
- **双语文档**: 英文和中文版本
- **指标本地化**: 支持本地化的指标名称和单位
- **文化适应**: 考虑不同的开发实践
- **一致术语**: 跨语言的统一词汇

## 快速开始

### 基础实现
```typescript
import { Tooltip } from 'recharts'
import { getTooltipConfig } from '@/components/shared/MonitorCard'

// 添加到任何图表组件
<AreaChart data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
  <Area dataKey="inMbps" stroke={colors.primary} />
</AreaChart>
```

### 自定义工具提示
```typescript
import { CustomTooltip } from '@/components/shared/MonitorCard'

<Tooltip 
  content={<CustomTooltip chartType="line" monitorType="transaction" />}
  allowEscapeViewBox={{ x: true, y: true }}
  offset={15}
/>
```

## 架构概述

### 组件层次结构
```
MonitorCard
├── 图表组件 (Area, Line, Bar等)
│   └── Tooltip
│       └── CustomTooltip
│           ├── 指标标签
│           ├── 颜色指示器  
│           ├── 格式化数值
│           └── 单位
└── 工具提示配置
    ├── 定位逻辑
    ├── 主题集成
    └── 光标样式
```

### 数据流
```
图表数据 → 指标映射 → 颜色分配 → 工具提示渲染
     ↓              ↓              ↓              ↓
监控类型 → getMetricInfo() → 设计令牌 → CustomTooltip
```

## 浏览器支持

| 浏览器 | 版本 | 支持级别 | 备注 |
|---------|---------|---------------|-------|
| Chrome | 90+ | ✅ 完全支持 | 推荐用于开发 |
| Firefox | 88+ | ✅ 完全支持 | 所有功能支持 |
| Safari | 14+ | ✅ 完全支持 | Webkit特定优化 |
| Edge | 90+ | ✅ 完全支持 | 基于Chromium的版本 |
| Mobile Safari | 14+ | ✅ 完全支持 | 触摸优化交互 |
| Chrome Mobile | 90+ | ✅ 完全支持 | 响应式设计测试 |

## 性能特征

### 包大小影响
- **核心工具提示**: ~2KB gzipped
- **图表集成**: 每种图表类型额外~1KB
- **设计令牌系统**: 组件间共享~0.5KB

### 运行时性能
- **渲染**: <16ms工具提示显示时间
- **内存**: <1MB额外堆使用
- **CPU**: 对图表交互的最小影响

## 入门指南

1. **阅读[组件API参考](./component-api-reference.md)** 获取完整接口文档
2. **遵循[实现指南](./implementation-guide.md)** 进行分步设置
3. **查看[使用示例](./usage-examples.md)** 了解常见模式
4. **使用[快速参考](./quick-reference.md)** 进行日常开发任务

## 贡献

### 文档更新
- 遵循既定的模板结构
- 包含英文和中文版本
- 提交前测试所有代码示例
- 添加新章节时更新交叉引用

### 代码贡献
- 维护TypeScript接口
- 遵循无障碍指南
- 包含全面测试
- 记录破坏性更改

## 支持

### 内部资源
- [设计系统文档](../../README.md)
- [MonitorCard组件指南](../components/monitor-card-system.md)
- [图表颜色系统](../../standards/chart-color-system.md)

### 外部资源
- [Recharts文档](https://recharts.org/en-US/)
- [WCAG 2.1指南](https://www.w3.org/WAI/WCAG21/quickref/)
- [CSS自定义属性](https://developer.mozilla.org/zh-CN/docs/Web/CSS/--*)

---

**语言**: [English](../en/README.md) | 中文  
**最后更新**: 2024年8月  
**版本**: 3.0 (设计令牌集成)
