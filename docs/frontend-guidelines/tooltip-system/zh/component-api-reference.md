# 组件API参考

## 概述

工具提示系统组件的完整TypeScript接口文档，包括所有属性、配置选项和类型定义。

## 核心接口

### CustomTooltipProps

自定义工具提示组件的主要接口。

```typescript
interface CustomTooltipProps {
  active?: boolean
  payload?: any[]
  label?: string
  chartType?: ChartType
  monitorType?: 'network' | 'transaction'
}
```

#### 属性

| 属性 | 类型 | 必需 | 默认值 | 描述 |
|----------|------|----------|---------|-------------|
| `active` | `boolean` | 否 | `false` | 工具提示当前是否激活/可见 |
| `payload` | `any[]` | 否 | `[]` | 悬停位置图表数据点数组 |
| `label` | `string` | 否 | `undefined` | 数据点的时间或类别标签 |
| `chartType` | `ChartType` | 否 | `'line'` | 显示工具提示的图表类型 |
| `monitorType` | `'network' \| 'transaction'` | 否 | `'network'` | 指标特定格式化的监控类型 |

#### 使用示例

```typescript
function CustomTooltip({ active, payload, label, chartType, monitorType }: CustomTooltipProps) {
  if (!active || !payload || !payload.length) {
    return null
  }
  
  return (
    <div className="bg-background/95 backdrop-blur-sm border border-border corner-sm shadow-lg p-3">
      {/* 工具提示内容 */}
    </div>
  )
}
```

### ChartType

支持的图表类型枚举。

```typescript
export type ChartType = 
  | 'area' 
  | 'line' 
  | 'bar' 
  | 'scatter' 
  | 'composed' 
  | 'step'
  | 'gradient-area' 
  | 'multi-line' 
  | 'stacked-bar' 
  | 'bubble' 
  | 'heatmap' 
  | 'radial'
  | 'waterfall' 
  | 'candlestick' 
  | 'pulse-wave'
```

#### 图表类型描述

| 类型 | 描述 | 使用场景 | 光标样式 |
|------|-------------|----------|--------------|
| `area` | 带渐变填充的面积图 | 网络流量可视化 | 虚线 |
| `line` | 简单折线图 | 交易趋势 | 虚线 |
| `bar` | 垂直柱状图 | 离散数据点 | 半透明覆盖 |
| `scatter` | 散点图 | 相关性分析 | 十字线 |
| `composed` | 组合图表类型 | 多指标显示 | 中性灰色 |
| `step` | 阶梯线图 | 成功率跟踪 | 虚线 |
| `gradient-area` | 多色渐变面积图 | 高级可视化 | 虚线 |
| `multi-line` | 多线系列 | 比较指标 | 虚线 |
| `stacked-bar` | 堆叠柱状图 | 资源使用分解 | 半透明覆盖 |
| `bubble` | 气泡图 | 多维数据 | 十字线 |
| `heatmap` | 颜色编码柱状图 | 强度可视化 | 半透明覆盖 |
| `radial` | 径向渐变面积图 | 圆形数据模式 | 虚线 |
| `waterfall` | 瀑布图 | 累积变化 | 半透明覆盖 |
| `candlestick` | 金融风格图表 | OHLC数据 | 虚线 |
| `pulse-wave` | 增强组合图 | 实时监控 | 虚线 |

### TooltipConfig

`getTooltipConfig()`函数返回的配置对象。

```typescript
interface TooltipConfig {
  content: React.ReactElement<CustomTooltipProps>
  allowEscapeViewBox: { x: boolean; y: boolean }
  offset: number
  position: { x: undefined; y: undefined }
  wrapperStyle: {
    zIndex: number
    pointerEvents: string
  }
}
```

#### 属性

| 属性 | 类型 | 描述 |
|----------|------|-------------|
| `content` | `React.ReactElement` | 自定义工具提示组件实例 |
| `allowEscapeViewBox` | `{ x: boolean; y: boolean }` | 允许工具提示扩展到图表边界之外 |
| `offset` | `number` | 距离悬停点的像素距离 |
| `position` | `{ x: undefined; y: undefined }` | 强制自动定位 |
| `wrapperStyle.zIndex` | `number` | 正确分层的Z轴索引 (9999) |
| `wrapperStyle.pointerEvents` | `string` | 防止工具提示交互 ('none') |

### MetricColors

指标特定颜色和标签信息的接口。

```typescript
interface MetricColors {
  label: string
  unit: string
  color: string
}
```

#### 属性

| 属性 | 类型 | 描述 | 示例 |
|----------|------|-------------|---------|
| `label` | `string` | 人类可读的指标名称 | "入站流量" |
| `unit` | `string` | 测量单位 | "Mbps" |
| `color` | `string` | CSS颜色值或自定义属性 | "var(--chart-network-inbound)" |

#### 预定义指标

##### 网络指标
```typescript
const networkMetrics = {
  inMbps: { label: '入站流量', unit: 'Mbps', color: 'var(--chart-network-inbound)' },
  outMbps: { label: '出站流量', unit: 'Mbps', color: 'var(--chart-network-outbound)' },
  rtt: { label: '往返时间', unit: 'ms', color: 'var(--chart-network-latency)' },
  loss: { label: '丢包率', unit: '%', color: 'var(--chart-network-loss)' },
  retrans: { label: '重传率', unit: '%', color: 'var(--chart-danger)' }
}
```

##### 交易指标
```typescript
const transactionMetrics = {
  req: { label: '请求数', unit: '/分钟', color: 'var(--chart-transaction-requests)' },
  successRate: { label: '成功率', unit: '%', color: 'var(--chart-transaction-success)' },
  respP95: { label: '响应时间P95', unit: 'ms', color: 'var(--chart-transaction-response)' },
  errorRate: { label: '错误率', unit: '%', color: 'var(--chart-transaction-error)' }
}
```

## 实用函数

### getTooltipConfig()

为图表创建标准化工具提示配置。

```typescript
function getTooltipConfig(
  chartType: ChartType, 
  monitorType: 'network' | 'transaction'
): TooltipConfig
```

#### 参数

| 参数 | 类型 | 必需 | 描述 |
|-----------|------|----------|-------------|
| `chartType` | `ChartType` | 是 | 光标样式的图表类型 |
| `monitorType` | `'network' \| 'transaction'` | 是 | 指标格式化的监控类型 |

#### 返回值

针对指定图表和监控类型优化设置的`TooltipConfig`对象。

#### 使用示例

```typescript
import { getTooltipConfig } from '@/components/shared/MonitorCard'

// 在图表组件中
<AreaChart data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
  <Area dataKey="inMbps" />
</AreaChart>
```

### getMetricInfo()

检索指标元数据，包括标签、单位和颜色。

```typescript
function getMetricInfo(dataKey: string): MetricColors
```

#### 参数

| 参数 | 类型 | 必需 | 描述 |
|-----------|------|----------|-------------|
| `dataKey` | `string` | 是 | 图表载荷中的数据键 |

#### 返回值

包含标签、单位和颜色信息的`MetricColors`对象。

#### 使用示例

```typescript
import { getMetricInfo } from '@/lib/chart-colors'

const metricInfo = getMetricInfo('inMbps')
// 返回: { label: '入站流量', unit: 'Mbps', color: 'var(--chart-network-inbound)' }
```

## CSS类

### 工具提示样式类

用于工具提示样式的标准Tailwind CSS类：

```typescript
const tooltipClasses = {
  container: 'bg-background/95 backdrop-blur-sm border border-border corner-sm shadow-lg p-3 min-w-[200px] max-w-[250px] z-50 relative',
  label: 'text-xs text-muted-foreground mb-2 font-medium',
  metricRow: 'flex items-center justify-between gap-3 py-1',
  colorIndicator: 'w-3 h-3 corner-xs flex-shrink-0',
  metricLabel: 'text-xs font-medium text-foreground flex-1',
  metricValue: 'text-xs font-mono text-foreground text-right'
}
```

### 设计令牌集成

工具提示样式使用CSS自定义属性确保主题一致性：

```css
/* 背景和边框 */
--background: /* 主题感知背景 */
--border: /* 主题感知边框颜色 */
--foreground: /* 主题感知文本颜色 */
--muted-foreground: /* 次要文本颜色 */

/* 图表特定颜色 */
--chart-network-inbound: /* 网络入站流量颜色 */
--chart-network-outbound: /* 网络出站流量颜色 */
--chart-transaction-requests: /* 交易请求颜色 */
--chart-transaction-success: /* 成功率颜色 */
```

## 类型守卫

### isValidChartType()

验证图表类型字符串的类型守卫。

```typescript
function isValidChartType(type: string): type is ChartType {
  const validTypes: ChartType[] = [
    'area', 'line', 'bar', 'scatter', 'composed', 'step',
    'gradient-area', 'multi-line', 'stacked-bar', 'bubble', 
    'heatmap', 'radial', 'waterfall', 'candlestick', 'pulse-wave'
  ]
  return validTypes.includes(type as ChartType)
}
```

### isValidMonitorType()

验证监控类型字符串的类型守卫。

```typescript
function isValidMonitorType(type: string): type is 'network' | 'transaction' {
  return type === 'network' || type === 'transaction'
}
```

## 错误处理

### 优雅降级

工具提示系统包含内置错误处理：

```typescript
function CustomTooltip({ active, payload, label, chartType, monitorType }: CustomTooltipProps) {
  // 无效状态的早期返回
  if (!active || !payload || !payload.length) {
    return null
  }
  
  try {
    // 工具提示渲染逻辑
    return (
      <div className="tooltip-container">
        {/* 内容 */}
      </div>
    )
  } catch (error) {
    // 回退到简单工具提示
    console.warn('工具提示渲染错误:', error)
    return (
      <div className="bg-background border border-border corner-sm p-2">
        <div className="text-xs text-foreground">
          {label || '数据点'}
        </div>
      </div>
    )
  }
}
```

### 常见错误场景

1. **缺少载荷数据**: 优雅地返回null
2. **无效图表类型**: 回退到默认折线图行为
3. **缺少指标信息**: 使用dataKey作为标签，单位为空
4. **主题令牌缺失**: 回退到中性颜色

---

**下一步**: [实现指南](./implementation-guide.md) | [返回概述](./README.md)  
**语言**: [English](../en/component-api-reference.md) | 中文
