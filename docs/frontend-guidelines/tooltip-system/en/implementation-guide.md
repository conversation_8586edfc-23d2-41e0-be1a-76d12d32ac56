# Implementation Guide

## Overview

Step-by-step guide for implementing tooltips in chart components, from basic setup to advanced customization.

## Prerequisites

### Required Dependencies

```json
{
  "recharts": "^2.8.0",
  "react": "^18.0.0",
  "typescript": "^5.0.0",
  "tailwindcss": "^3.3.0"
}
```

### Required Imports

```typescript
// Chart components
import { Tooltip } from 'recharts'

// Utility functions
import { getTooltipConfig, CustomTooltip } from '@/components/shared/MonitorCard'
import { getMetricInfo, getMonitorTypeColors } from '@/lib/chart-colors'

// Type definitions
import type { ChartType } from '@/components/shared/MonitorCard'
```

## Basic Implementation

### Step 1: Simple Tooltip Setup

Add a basic tooltip to any Recharts component:

```typescript
import { AreaChart, Area, Tooltip } from 'recharts'

function BasicChart({ data }: { data: any[] }) {
  return (
    <AreaChart data={data} width={300} height={200}>
      <Tooltip />
      <Area dataKey="value" stroke="#8884d8" fill="#8884d8" />
    </AreaChart>
  )
}
```

### Step 2: Add Custom Tooltip

Replace the default tooltip with the custom implementation:

```typescript
import { AreaChart, Area, Tooltip } from 'recharts'
import { getTooltipConfig } from '@/components/shared/MonitorCard'

function CustomChart({ data }: { data: any[] }) {
  return (
    <AreaChart data={data} width={300} height={200}>
      <Tooltip {...getTooltipConfig('area', 'network')} />
      <Area dataKey="inMbps" stroke="var(--chart-network-inbound)" />
    </AreaChart>
  )
}
```

### Step 3: Configure for Monitor Type

Specify the correct monitor type for proper metric formatting:

```typescript
function NetworkChart({ data }: { data: any[] }) {
  return (
    <AreaChart data={data} width={300} height={200}>
      <Tooltip {...getTooltipConfig('area', 'network')} />
      <Area dataKey="inMbps" stroke="var(--chart-network-inbound)" />
      <Area dataKey="outMbps" stroke="var(--chart-network-outbound)" />
    </AreaChart>
  )
}

function TransactionChart({ data }: { data: any[] }) {
  return (
    <LineChart data={data} width={300} height={200}>
      <Tooltip {...getTooltipConfig('line', 'transaction')} />
      <Line dataKey="req" stroke="var(--chart-transaction-requests)" />
      <Line dataKey="successRate" stroke="var(--chart-transaction-success)" />
    </LineChart>
  )
}
```

## Advanced Implementation

### Custom Tooltip Component

Create a specialized tooltip for specific use cases:

```typescript
import { CustomTooltipProps } from '@/components/shared/MonitorCard'
import { getMetricInfo } from '@/lib/chart-colors'

function AdvancedTooltip({ active, payload, label, chartType, monitorType }: CustomTooltipProps) {
  if (!active || !payload || !payload.length) {
    return null
  }

  return (
    <div className="bg-background/95 backdrop-blur-sm border border-border corner-sm shadow-lg p-3 min-w-[200px] max-w-[250px] z-50">
      {/* Custom header */}
      <div className="text-xs text-muted-foreground mb-2 font-medium border-b border-border pb-1">
        {label} - {chartType?.toUpperCase()} Chart
      </div>
      
      {/* Metrics */}
      <div className="space-y-1">
        {payload.map((entry, index) => {
          const metricInfo = getMetricInfo(entry.dataKey)
          return (
            <div key={index} className="flex items-center justify-between gap-3">
              <div className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 corner-xs flex-shrink-0"
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-xs font-medium text-foreground">
                  {metricInfo.label}
                </span>
              </div>
              <span className="text-xs font-mono text-foreground">
                {entry.value?.toFixed(1)}{metricInfo.unit}
              </span>
            </div>
          )
        })}
      </div>
      
      {/* Footer with additional info */}
      <div className="text-xs text-muted-foreground mt-2 pt-1 border-t border-border">
        Monitor Type: {monitorType}
      </div>
    </div>
  )
}

// Usage
<Tooltip content={<AdvancedTooltip chartType="area" monitorType="network" />} />
```

### Dynamic Configuration

Implement dynamic tooltip configuration based on data:

```typescript
function DynamicChart({ data, monitorType, chartType }: {
  data: any[]
  monitorType: 'network' | 'transaction'
  chartType: ChartType
}) {
  const tooltipConfig = useMemo(() => {
    return getTooltipConfig(chartType, monitorType)
  }, [chartType, monitorType])

  const colors = getMonitorTypeColors(monitorType)

  return (
    <AreaChart data={data} width={300} height={200}>
      <Tooltip {...tooltipConfig} />
      <Area 
        dataKey={monitorType === 'network' ? 'inMbps' : 'req'} 
        stroke={colors.primary} 
      />
    </AreaChart>
  )
}
```

## Chart-Specific Implementations

### Area Chart with Gradient

```typescript
function GradientAreaChart({ data, monitorId }: { data: any[], monitorId: string }) {
  const colors = getMonitorTypeColors('network')
  
  return (
    <AreaChart data={data} width={300} height={200}>
      <defs>
        <linearGradient id={`gradient-${monitorId}`} x1="0" y1="0" x2="0" y2="1">
          <stop offset="5%" stopColor={colors.primary} stopOpacity={0.8}/>
          <stop offset="95%" stopColor={colors.primary} stopOpacity={0.1}/>
        </linearGradient>
      </defs>
      
      <Tooltip {...getTooltipConfig('gradient-area', 'network')} />
      
      <Area
        type="monotone"
        dataKey="inMbps"
        stroke={colors.primary}
        fill={`url(#gradient-${monitorId})`}
        strokeWidth={2}
        dot={false}
      />
    </AreaChart>
  )
}
```

### Multi-Line Chart

```typescript
function MultiLineChart({ data }: { data: any[] }) {
  const colors = getMonitorTypeColors('transaction')
  
  return (
    <LineChart data={data} width={300} height={200}>
      <Tooltip {...getTooltipConfig('multi-line', 'transaction')} />
      
      <Line
        type="monotone"
        dataKey="req"
        stroke={colors.primary}
        strokeWidth={2}
        dot={false}
      />
      <Line
        type="monotone"
        dataKey="successRate"
        stroke={colors.secondary}
        strokeWidth={2}
        dot={false}
      />
      <Line
        type="monotone"
        dataKey="respP95"
        stroke={colors.accent}
        strokeWidth={1}
        strokeDasharray="3 3"
        dot={false}
      />
    </LineChart>
  )
}
```

### Composed Chart

```typescript
function ComposedChart({ data }: { data: any[] }) {
  const colors = getMonitorTypeColors('network')
  
  return (
    <ComposedChart data={data} width={300} height={200}>
      <Tooltip 
        {...getTooltipConfig('composed', 'network')}
        cursor={{ stroke: 'var(--chart-grid)', strokeWidth: 1, strokeDasharray: '3 3' }}
      />
      
      <Bar dataKey="inMbps" fill={colors.primary} opacity={0.6} />
      <Line
        type="monotone"
        dataKey="rtt"
        stroke={colors.secondary}
        strokeWidth={2}
        dot={{ fill: colors.secondary, strokeWidth: 2, r: 3 }}
      />
    </ComposedChart>
  )
}
```

## Container Integration

### MonitorCard Integration

Integrate tooltips within the MonitorCard component structure:

```typescript
function MonitorCardWithTooltip({ monitor }: { monitor: MonitorCardData }) {
  const [isClient, setIsClient] = useState(false)
  
  useEffect(() => {
    setIsClient(true)
  }, [])
  
  if (!isClient || !monitor.showMetrics) {
    return <SimpleCard monitor={monitor} />
  }
  
  return (
    <Card className="monitor-card-size overflow-visible">
      <CardContent className="p-3">
        <div className="h-32">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={generateData(monitor.type)}>
              <Tooltip {...getTooltipConfig('area', monitor.type)} />
              <Area 
                dataKey={monitor.type === 'network' ? 'inMbps' : 'req'}
                stroke="var(--chart-primary)"
                fill="var(--chart-primary)"
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
```

### Responsive Container Setup

Ensure proper responsive behavior:

```typescript
function ResponsiveChartWithTooltip({ data, chartType, monitorType }: {
  data: any[]
  chartType: ChartType
  monitorType: 'network' | 'transaction'
}) {
  return (
    <div className="w-full h-64">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data}>
          <Tooltip 
            {...getTooltipConfig(chartType, monitorType)}
            // Ensure tooltip stays within viewport on small screens
            position={{ x: undefined, y: undefined }}
            allowEscapeViewBox={{ x: true, y: false }}
          />
          <Area dataKey="value" stroke="var(--chart-primary)" />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  )
}
```

## Error Handling

### Graceful Fallbacks

Implement error boundaries and fallbacks:

```typescript
function SafeTooltipChart({ data, chartType, monitorType }: {
  data: any[]
  chartType: ChartType
  monitorType: 'network' | 'transaction'
}) {
  const [hasError, setHasError] = useState(false)
  
  if (hasError) {
    return (
      <div className="w-full h-32 flex items-center justify-center bg-muted/50 corner-sm">
        <span className="text-xs text-muted-foreground">Chart unavailable</span>
      </div>
    )
  }
  
  try {
    return (
      <AreaChart data={data} width={300} height={200}>
        <Tooltip 
          {...getTooltipConfig(chartType, monitorType)}
          // Fallback tooltip if custom component fails
          content={({ active, payload, label }) => {
            if (!active || !payload?.length) return null
            return (
              <div className="bg-background border border-border corner-sm p-2">
                <div className="text-xs text-foreground">{label}</div>
                {payload.map((entry, index) => (
                  <div key={index} className="text-xs text-muted-foreground">
                    {entry.dataKey}: {entry.value}
                  </div>
                ))}
              </div>
            )
          }}
        />
        <Area dataKey="value" stroke="var(--chart-primary)" />
      </AreaChart>
    )
  } catch (error) {
    setHasError(true)
    return null
  }
}
```

## Testing Implementation

### Unit Test Setup

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { CustomTooltip } from '@/components/shared/MonitorCard'

describe('CustomTooltip', () => {
  const mockPayload = [
    { dataKey: 'inMbps', value: 45.2, color: '#3b82f6' },
    { dataKey: 'outMbps', value: 23.1, color: '#06b6d4' }
  ]
  
  it('renders tooltip with network metrics', () => {
    render(
      <CustomTooltip
        active={true}
        payload={mockPayload}
        label="12:30 PM"
        chartType="area"
        monitorType="network"
      />
    )
    
    expect(screen.getByText('Inbound Traffic')).toBeInTheDocument()
    expect(screen.getByText('45.2Mbps')).toBeInTheDocument()
  })
})
```

### Integration Testing

```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { AreaChart, Area, Tooltip } from 'recharts'
import { getTooltipConfig } from '@/components/shared/MonitorCard'

describe('Chart Tooltip Integration', () => {
  const mockData = [
    { time: '12:30', inMbps: 45.2, outMbps: 23.1 }
  ]
  
  it('displays tooltip on hover', async () => {
    render(
      <AreaChart data={mockData} width={300} height={200}>
        <Tooltip {...getTooltipConfig('area', 'network')} />
        <Area dataKey="inMbps" />
      </AreaChart>
    )
    
    // Simulate hover event
    const chartArea = screen.getByRole('img') // SVG element
    fireEvent.mouseEnter(chartArea)
    
    // Check if tooltip appears
    expect(screen.getByText('Inbound Traffic')).toBeInTheDocument()
  })
})
```

---

**Next**: [Integration Patterns](./integration-patterns.md) | [Previous](./component-api-reference.md)  
**Language**: English | [中文](../zh/implementation-guide.md)
