# Quick Reference

## Overview

Copy-paste ready code snippets and common patterns for daily development tasks with the tooltip system.

## Common Tasks

### 🚀 Quick Setup

#### Basic Tooltip
```typescript
import { Tooltip } from 'recharts'
import { getTooltipConfig } from '@/components/shared/MonitorCard'

<AreaChart data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
  <Area dataKey="inMbps" stroke="var(--chart-network-inbound)" />
</AreaChart>
```

#### Custom Tooltip
```typescript
import { CustomTooltip } from '@/components/shared/MonitorCard'

<Tooltip 
  content={<CustomTooltip chartType="line" monitorType="transaction" />}
  allowEscapeViewBox={{ x: true, y: true }}
  offset={15}
/>
```

### 📊 Chart Types

#### Area Chart
```typescript
<AreaChart data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
  <Area dataKey="inMbps" stroke={colors.primary} fill={colors.primary} fillOpacity={0.3} />
</AreaChart>
```

#### Line Chart
```typescript
<LineChart data={data}>
  <Tooltip {...getTooltipConfig('line', 'transaction')} />
  <Line dataKey="req" stroke={colors.primary} strokeWidth={2} dot={false} />
</LineChart>
```

#### Bar Chart
```typescript
<BarChart data={data}>
  <Tooltip {...getTooltipConfig('bar', 'network')} />
  <Bar dataKey="inMbps" fill={colors.primary} opacity={0.8} />
</BarChart>
```

#### Composed Chart
```typescript
<ComposedChart data={data}>
  <Tooltip {...getTooltipConfig('composed', 'network')} />
  <Bar dataKey="inMbps" fill={colors.primary} opacity={0.6} />
  <Line dataKey="rtt" stroke={colors.secondary} strokeWidth={2} />
</ComposedChart>
```

### 🎨 Styling

#### Custom Colors
```typescript
const colors = getMonitorTypeColors('network')
// colors.primary, colors.secondary, colors.accent

const uiColors = getChartUIColors()
// uiColors.grid, uiColors.warning, uiColors.danger
```

#### Cursor Styles
```typescript
// Dashed line cursor
cursor={{ stroke: colors.primary, strokeWidth: 1, strokeDasharray: '3 3' }}

// Semi-transparent overlay
cursor={{ fill: colors.primary, fillOpacity: 0.1 }}

// Crosshair cursor
cursor={{ stroke: colors.primary, strokeWidth: 1 }}
```

#### Tooltip Container
```typescript
const tooltipClasses = "bg-background/95 backdrop-blur-sm border border-border corner-sm shadow-lg p-3 min-w-[200px] max-w-[250px] z-50"
```

### 🔧 Configuration

#### Positioning
```typescript
// Allow escape from chart boundaries
allowEscapeViewBox={{ x: true, y: true }}

// Fixed offset from cursor
offset={15}

// Automatic positioning
position={{ x: undefined, y: undefined }}

// Z-index management
wrapperStyle={{ zIndex: 9999, pointerEvents: 'none' }}
```

#### Responsive Setup
```typescript
<div className="w-full h-64">
  <ResponsiveContainer width="100%" height="100%">
    <AreaChart data={data}>
      <Tooltip {...getTooltipConfig('area', 'network')} />
      <Area dataKey="value" />
    </AreaChart>
  </ResponsiveContainer>
</div>
```

## Code Snippets

### 📋 Metric Information

#### Get Metric Details
```typescript
import { getMetricInfo } from '@/lib/chart-colors'

const metricInfo = getMetricInfo('inMbps')
// { label: 'Inbound Traffic', unit: 'Mbps', color: 'var(--chart-network-inbound)' }
```

#### Format Values
```typescript
const formatValue = (value: number, unit: string) => {
  if (unit === '%') return `${value.toFixed(1)}%`
  if (unit === 'ms') return `${value.toFixed(0)}ms`
  if (unit === 'Mbps') return `${value.toFixed(1)}Mbps`
  if (unit === '/min') return `${value.toFixed(0)}/min`
  return `${value.toFixed(1)}${unit}`
}
```

### 🎯 Monitor Types

#### Network Monitor
```typescript
const networkData = [
  { time: '12:30', inMbps: 45.2, outMbps: 23.1, rtt: 12.5, loss: 0.1 }
]

<AreaChart data={networkData}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
  <Area dataKey="inMbps" stroke="var(--chart-network-inbound)" />
  <Area dataKey="outMbps" stroke="var(--chart-network-outbound)" />
</AreaChart>
```

#### Transaction Monitor
```typescript
const transactionData = [
  { time: '12:30', req: 1250, successRate: 99.8, respP95: 45, errorRate: 0.2 }
]

<LineChart data={transactionData}>
  <Tooltip {...getTooltipConfig('line', 'transaction')} />
  <Line dataKey="req" stroke="var(--chart-transaction-requests)" />
  <Line dataKey="successRate" stroke="var(--chart-transaction-success)" />
</LineChart>
```

### 🛠️ Utilities

#### Type Guards
```typescript
const isValidChartType = (type: string): type is ChartType => {
  return ['area', 'line', 'bar', 'scatter', 'composed'].includes(type)
}

const isValidMonitorType = (type: string): type is 'network' | 'transaction' => {
  return type === 'network' || type === 'transaction'
}
```

#### Error Handling
```typescript
const SafeTooltip = ({ chartType, monitorType, ...props }) => {
  try {
    return <CustomTooltip chartType={chartType} monitorType={monitorType} {...props} />
  } catch (error) {
    console.warn('Tooltip error:', error)
    return (
      <div className="bg-background border border-border corner-sm p-2">
        <div className="text-xs text-foreground">Data point</div>
      </div>
    )
  }
}
```

## Troubleshooting

### ❌ Common Issues

#### Tooltip Not Showing
```typescript
// ✅ Ensure chart has data
if (!data || data.length === 0) return null

// ✅ Check ResponsiveContainer
<ResponsiveContainer width="100%" height="100%">
  <AreaChart data={data}>
    <Tooltip {...getTooltipConfig('area', 'network')} />
  </AreaChart>
</ResponsiveContainer>

// ✅ Verify container overflow
<div className="overflow-visible"> {/* Not overflow-hidden */}
```

#### Tooltip Positioning Issues
```typescript
// ✅ Allow escape from boundaries
allowEscapeViewBox={{ x: true, y: true }}

// ✅ Set proper z-index
wrapperStyle={{ zIndex: 9999 }}

// ✅ Prevent pointer events
wrapperStyle={{ pointerEvents: 'none' }}
```

#### Missing Metric Labels
```typescript
// ✅ Import metric utilities
import { getMetricInfo } from '@/lib/chart-colors'

// ✅ Check data key mapping
const metricInfo = getMetricInfo(entry.dataKey)
if (!metricInfo.label) {
  console.warn(`No metric info for: ${entry.dataKey}`)
}
```

### ✅ Quick Fixes

#### Force Re-render
```typescript
const [key, setKey] = useState(0)

// Trigger re-render
useEffect(() => {
  setKey(prev => prev + 1)
}, [data])

<AreaChart key={key} data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
</AreaChart>
```

#### Debug Tooltip Data
```typescript
<Tooltip 
  content={({ active, payload, label }) => {
    console.log('Tooltip data:', { active, payload, label })
    return <CustomTooltip active={active} payload={payload} label={label} />
  }}
/>
```

## Performance Tips

### 🚀 Optimization

#### Memoize Configuration
```typescript
const tooltipConfig = useMemo(() => 
  getTooltipConfig(chartType, monitorType), 
  [chartType, monitorType]
)

<Tooltip {...tooltipConfig} />
```

#### Lazy Loading
```typescript
const LazyTooltip = lazy(() => import('./CustomTooltip'))

<Suspense fallback={<div>Loading...</div>}>
  <LazyTooltip chartType="area" monitorType="network" />
</Suspense>
```

#### Debounce Updates
```typescript
const debouncedData = useMemo(() => 
  debounce(data, 100), 
  [data]
)
```

## Testing Snippets

### 🧪 Unit Tests

#### Mock Tooltip Props
```typescript
const mockTooltipProps = {
  active: true,
  payload: [
    { dataKey: 'inMbps', value: 45.2, color: '#3b82f6' }
  ],
  label: '12:30 PM',
  chartType: 'area' as ChartType,
  monitorType: 'network' as const
}
```

#### Test Tooltip Rendering
```typescript
import { render, screen } from '@testing-library/react'

test('renders tooltip with correct metrics', () => {
  render(<CustomTooltip {...mockTooltipProps} />)
  
  expect(screen.getByText('Inbound Traffic')).toBeInTheDocument()
  expect(screen.getByText('45.2Mbps')).toBeInTheDocument()
})
```

### 🔍 Integration Tests

#### Hover Simulation
```typescript
import { fireEvent } from '@testing-library/react'

const chartElement = screen.getByRole('img') // SVG
fireEvent.mouseEnter(chartElement, { clientX: 100, clientY: 100 })

expect(screen.getByText('Tooltip content')).toBeInTheDocument()
```

## CSS Variables

### 🎨 Design Tokens

#### Chart Colors
```css
--chart-primary: hsl(var(--primary))
--chart-secondary: hsl(var(--secondary))
--chart-accent: hsl(var(--accent))

--chart-network-inbound: #3b82f6
--chart-network-outbound: #06b6d4
--chart-network-latency: #10b981

--chart-transaction-requests: #f59e0b
--chart-transaction-success: #10b981
--chart-transaction-response: #ef4444
```

#### UI Colors
```css
--background: hsl(var(--background))
--foreground: hsl(var(--foreground))
--muted-foreground: hsl(var(--muted-foreground))
--border: hsl(var(--border))
```

---

**Related**: [Implementation Guide](./implementation-guide.md) | [Troubleshooting](./troubleshooting-guide.md)  
**Language**: English | [中文](../zh/quick-reference.md)
