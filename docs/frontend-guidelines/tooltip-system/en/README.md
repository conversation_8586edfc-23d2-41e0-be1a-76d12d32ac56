# Tooltip System Documentation

## Overview

This comprehensive guide covers the tooltip system implementation for chart data visualization in the Cape Horn Monitor project. The system provides interactive tooltips for MonitorCard components with advanced positioning, design system integration, and cross-browser compatibility.

## Documentation Structure

### Core Documentation
- [**Component API Reference**](./component-api-reference.md) - Complete TypeScript interfaces and props
- [**Implementation Guide**](./implementation-guide.md) - Step-by-step implementation instructions
- [**Integration Patterns**](./integration-patterns.md) - How tooltips integrate with other systems
- [**Usage Examples**](./usage-examples.md) - Complete working code examples

### Developer Resources
- [**Quick Reference**](./quick-reference.md) - Common tasks and code snippets
- [**Troubleshooting Guide**](./troubleshooting-guide.md) - Common issues and solutions
- [**Performance Guide**](./performance-guide.md) - Optimization best practices
- [**Testing Guide**](./testing-guide.md) - Testing approaches and requirements

### Quality Assurance
- [**Accessibility Guide**](./accessibility-guide.md) - WCAG compliance and best practices
- [**Cross-Browser Compatibility**](./cross-browser-compatibility.md) - Browser support and testing
- [**Mobile Responsiveness**](./mobile-responsiveness.md) - Touch and mobile considerations
- [**Validation Checklist**](./validation-checklist.md) - Implementation validation

## Key Features

### 🎯 Smart Positioning
- **Escape ViewBox**: Tooltips can extend beyond chart boundaries
- **Collision Detection**: Automatic positioning to avoid content blocking
- **Z-Index Management**: Proper layering with other UI elements
- **Offset Control**: Configurable spacing from hover point

### 🎨 Design System Integration
- **CSS Custom Properties**: Uses design tokens for theming
- **Theme Awareness**: Automatic light/dark mode adaptation
- **Color Coordination**: Metric colors match chart elements
- **Typography Consistency**: Follows established text hierarchy

### 📊 Chart Type Support
- **15+ Chart Types**: Area, line, bar, scatter, composed, and more
- **Metric-Specific**: Different tooltips for network vs transaction data
- **Dynamic Content**: Adapts to available data and chart configuration
- **Interactive Cursors**: Chart-specific cursor styling

### 🌐 Internationalization Ready
- **Bilingual Documentation**: English and Chinese versions
- **Metric Localization**: Supports localized metric names and units
- **Cultural Adaptation**: Considers different development practices
- **Consistent Terminology**: Unified vocabulary across languages

## Quick Start

### Basic Implementation
```typescript
import { Tooltip } from 'recharts'
import { getTooltipConfig } from '@/components/shared/MonitorCard'

// Add to any chart component
<AreaChart data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
  <Area dataKey="inMbps" stroke={colors.primary} />
</AreaChart>
```

### Custom Tooltip
```typescript
import { CustomTooltip } from '@/components/shared/MonitorCard'

<Tooltip 
  content={<CustomTooltip chartType="line" monitorType="transaction" />}
  allowEscapeViewBox={{ x: true, y: true }}
  offset={15}
/>
```

## Architecture Overview

### Component Hierarchy
```
MonitorCard
├── Chart Components (Area, Line, Bar, etc.)
│   └── Tooltip
│       └── CustomTooltip
│           ├── Metric Labels
│           ├── Color Indicators  
│           ├── Formatted Values
│           └── Units
└── Tooltip Configuration
    ├── Positioning Logic
    ├── Theme Integration
    └── Cursor Styling
```

### Data Flow
```
Chart Data → Metric Mapping → Color Assignment → Tooltip Rendering
     ↓              ↓              ↓              ↓
Monitor Type → getMetricInfo() → Design Tokens → CustomTooltip
```

## Browser Support

| Browser | Version | Support Level | Notes |
|---------|---------|---------------|-------|
| Chrome | 90+ | ✅ Full | Recommended for development |
| Firefox | 88+ | ✅ Full | All features supported |
| Safari | 14+ | ✅ Full | Webkit-specific optimizations |
| Edge | 90+ | ✅ Full | Chromium-based versions |
| Mobile Safari | 14+ | ✅ Full | Touch-optimized interactions |
| Chrome Mobile | 90+ | ✅ Full | Responsive design tested |

## Performance Characteristics

### Bundle Impact
- **Core Tooltip**: ~2KB gzipped
- **Chart Integration**: ~1KB additional per chart type
- **Design Token System**: ~0.5KB shared across components

### Runtime Performance
- **Rendering**: <16ms tooltip display time
- **Memory**: <1MB additional heap usage
- **CPU**: Minimal impact on chart interactions

## Getting Started

1. **Read the [Component API Reference](./component-api-reference.md)** for complete interface documentation
2. **Follow the [Implementation Guide](./implementation-guide.md)** for step-by-step setup
3. **Check [Usage Examples](./usage-examples.md)** for common patterns
4. **Use [Quick Reference](./quick-reference.md)** for daily development tasks

## Contributing

### Documentation Updates
- Follow the established template structure
- Include both English and Chinese versions
- Test all code examples before committing
- Update cross-references when adding new sections

### Code Contributions
- Maintain TypeScript interfaces
- Follow accessibility guidelines
- Include comprehensive tests
- Document breaking changes

## Support

### Internal Resources
- [Design System Documentation](../../README.md)
- [MonitorCard Component Guide](../components/monitor-card-system.md)
- [Chart Color System](../../standards/chart-color-system.md)

### External Resources
- [Recharts Documentation](https://recharts.org/en-US/)
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [CSS Custom Properties](https://developer.mozilla.org/en-US/docs/Web/CSS/--*)

---

**Language**: English | [中文](../zh/README.md)  
**Last Updated**: August 2024  
**Version**: 3.0 (Design Token Integration)
