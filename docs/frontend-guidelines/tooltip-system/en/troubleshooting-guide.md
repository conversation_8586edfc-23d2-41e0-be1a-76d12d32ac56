# Troubleshooting Guide

## Overview

Common issues, solutions, and debugging techniques for the tooltip system implementation.

## Common Issues

### 🚫 Tooltip Not Appearing

#### Symptoms
- Hovering over chart shows no tooltip
- Tooltip flickers or appears briefly
- No visual feedback on chart interaction

#### Possible Causes & Solutions

##### 1. Missing or Empty Data
```typescript
// ❌ Problem: Empty data array
const data = []

// ✅ Solution: Ensure data exists
if (!data || data.length === 0) {
  return <div>No data available</div>
}

// ✅ Better: Add loading state
const [data, setData] = useState([])
const [isLoading, setIsLoading] = useState(true)

if (isLoading) return <LoadingSpinner />
if (!data.length) return <NoDataMessage />
```

##### 2. Container Overflow Issues
```typescript
// ❌ Problem: Hidden overflow
<div className="overflow-hidden">
  <AreaChart data={data}>
    <Tooltip {...getTooltipConfig('area', 'network')} />
  </AreaChart>
</div>

// ✅ Solution: Allow overflow
<div className="overflow-visible">
  <AreaChart data={data}>
    <Tooltip {...getTooltipConfig('area', 'network')} />
  </AreaChart>
</div>
```

##### 3. Missing ResponsiveContainer
```typescript
// ❌ Problem: Fixed dimensions without container
<AreaChart width={300} height={200} data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
</AreaChart>

// ✅ Solution: Use ResponsiveContainer
<ResponsiveContainer width="100%" height="100%">
  <AreaChart data={data}>
    <Tooltip {...getTooltipConfig('area', 'network')} />
  </AreaChart>
</ResponsiveContainer>
```

##### 4. Z-Index Conflicts
```typescript
// ❌ Problem: Tooltip behind other elements
<Tooltip content={<CustomTooltip />} />

// ✅ Solution: Ensure proper z-index
<Tooltip 
  {...getTooltipConfig('area', 'network')}
  wrapperStyle={{ zIndex: 9999 }}
/>
```

### 🎯 Positioning Problems

#### Symptoms
- Tooltip appears in wrong location
- Tooltip gets cut off at edges
- Tooltip blocks chart content

#### Solutions

##### 1. Enable Escape ViewBox
```typescript
// ✅ Allow tooltip to escape chart boundaries
<Tooltip 
  {...getTooltipConfig('area', 'network')}
  allowEscapeViewBox={{ x: true, y: true }}
/>
```

##### 2. Adjust Offset
```typescript
// ✅ Increase distance from cursor
<Tooltip 
  {...getTooltipConfig('area', 'network')}
  offset={20} // Increase from default 15
/>
```

##### 3. Force Automatic Positioning
```typescript
// ✅ Let Recharts handle positioning
<Tooltip 
  {...getTooltipConfig('area', 'network')}
  position={{ x: undefined, y: undefined }}
/>
```

### 🎨 Styling Issues

#### Symptoms
- Tooltip appears unstyled or with wrong colors
- Text is unreadable or poorly formatted
- Inconsistent appearance across themes

#### Solutions

##### 1. Missing Design Tokens
```typescript
// ❌ Problem: Hardcoded colors
<div style={{ backgroundColor: '#ffffff', color: '#000000' }}>

// ✅ Solution: Use CSS custom properties
<div className="bg-background text-foreground">
```

##### 2. Theme Not Applied
```typescript
// ✅ Ensure theme provider wraps component
import { ThemeProvider } from 'next-themes'

<ThemeProvider attribute="class" defaultTheme="system">
  <YourApp />
</ThemeProvider>
```

##### 3. CSS Classes Not Loading
```typescript
// ✅ Verify Tailwind CSS is properly configured
// Check tailwind.config.js includes tooltip classes
module.exports = {
  content: [
    './components/**/*.{js,ts,jsx,tsx}',
    './app/**/*.{js,ts,jsx,tsx}',
  ],
  // ...
}
```

### 📊 Data Display Issues

#### Symptoms
- Wrong metric labels or units
- Missing or incorrect values
- Inconsistent formatting

#### Solutions

##### 1. Metric Mapping Issues
```typescript
// ❌ Problem: Unknown data key
const metricInfo = getMetricInfo('unknownKey')
// Returns: { label: 'unknownKey', unit: '', color: 'var(--chart-grid)' }

// ✅ Solution: Verify data keys match metric definitions
const validKeys = ['inMbps', 'outMbps', 'rtt', 'req', 'successRate']
const dataKey = validKeys.includes(entry.dataKey) ? entry.dataKey : 'default'
```

##### 2. Value Formatting
```typescript
// ✅ Add proper value formatting
const formatValue = (value: number, unit: string) => {
  if (typeof value !== 'number' || isNaN(value)) return 'N/A'
  
  switch (unit) {
    case '%': return `${value.toFixed(1)}%`
    case 'ms': return `${Math.round(value)}ms`
    case 'Mbps': return `${value.toFixed(1)}Mbps`
    case '/min': return `${Math.round(value)}/min`
    default: return `${value.toFixed(1)}${unit}`
  }
}
```

##### 3. Monitor Type Mismatch
```typescript
// ✅ Ensure correct monitor type
const monitorType = monitor.type === 'transaction' ? 'transaction' : 'network'

<Tooltip {...getTooltipConfig(chartType, monitorType)} />
```

## Debugging Techniques

### 🔍 Debug Tooltip Data

#### Log Tooltip Props
```typescript
<Tooltip 
  content={({ active, payload, label }) => {
    console.log('Tooltip Debug:', {
      active,
      payload: payload?.map(p => ({ dataKey: p.dataKey, value: p.value })),
      label,
      chartType,
      monitorType
    })
    return <CustomTooltip active={active} payload={payload} label={label} />
  }}
/>
```

#### Inspect Chart Data
```typescript
useEffect(() => {
  console.log('Chart Data:', {
    dataLength: data.length,
    sampleData: data[0],
    dataKeys: data[0] ? Object.keys(data[0]) : [],
    monitorType,
    chartType
  })
}, [data, monitorType, chartType])
```

### 🧪 Test Tooltip Rendering

#### Force Tooltip Visibility
```typescript
// Temporarily force tooltip to be visible
<Tooltip 
  active={true} // Force active
  payload={[{ dataKey: 'inMbps', value: 45.2, color: '#3b82f6' }]}
  label="Debug Mode"
  content={<CustomTooltip chartType="area" monitorType="network" />}
/>
```

#### Minimal Test Component
```typescript
function TooltipTest() {
  const testData = [
    { time: '12:30', inMbps: 45.2, outMbps: 23.1 }
  ]
  
  return (
    <div className="w-64 h-32 border border-border">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={testData}>
          <Tooltip {...getTooltipConfig('area', 'network')} />
          <Area dataKey="inMbps" stroke="var(--chart-network-inbound)" />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  )
}
```

### 🔧 Performance Debugging

#### Check Re-renders
```typescript
import { memo, useCallback } from 'react'

const OptimizedTooltip = memo(({ active, payload, label, chartType, monitorType }) => {
  console.log('Tooltip re-render:', { active, chartType, monitorType })
  return <CustomTooltip {...props} />
})

// Use stable references
const tooltipConfig = useMemo(() => 
  getTooltipConfig(chartType, monitorType), 
  [chartType, monitorType]
)
```

#### Monitor Bundle Size
```bash
# Analyze bundle impact
npm run build
npm run analyze # If configured

# Check specific imports
import { getTooltipConfig } from '@/components/shared/MonitorCard'
// vs
import MonitorCard, { getTooltipConfig } from '@/components/shared/MonitorCard'
```

## Browser-Specific Issues

### 🌐 Safari Issues

#### SVG Rendering Problems
```typescript
// ✅ Add explicit SVG namespace
<svg xmlns="http://www.w3.org/2000/svg">
  {/* Chart content */}
</svg>

// ✅ Force repaint on data changes
const [key, setKey] = useState(0)
useEffect(() => {
  setKey(prev => prev + 1)
}, [data])

<AreaChart key={key} data={data}>
```

#### CSS Custom Properties
```css
/* ✅ Ensure fallbacks for older Safari */
.tooltip {
  background-color: var(--background, #ffffff);
  color: var(--foreground, #000000);
}
```

### 🔥 Firefox Issues

#### Backdrop Filter Support
```css
/* ✅ Add fallback for backdrop-blur */
.tooltip {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  /* Fallback for older Firefox */
  background-color: rgba(255, 255, 255, 0.98);
}
```

### 📱 Mobile Issues

#### Touch Events
```typescript
// ✅ Handle touch events properly
<AreaChart 
  data={data}
  onTouchStart={(e) => {
    // Prevent default touch behavior if needed
    e.preventDefault()
  }}
>
  <Tooltip 
    {...getTooltipConfig('area', 'network')}
    // Adjust for touch
    offset={25}
  />
</AreaChart>
```

#### Viewport Issues
```css
/* ✅ Ensure proper viewport meta tag */
<meta name="viewport" content="width=device-width, initial-scale=1" />

/* ✅ Responsive tooltip sizing */
.tooltip {
  min-width: min(200px, 80vw);
  max-width: min(250px, 90vw);
}
```

## Error Recovery

### 🛡️ Error Boundaries

#### Tooltip Error Boundary
```typescript
class TooltipErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false }
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true }
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Tooltip Error:', error, errorInfo)
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="bg-background border border-border corner-sm p-2">
          <div className="text-xs text-muted-foreground">
            Tooltip unavailable
          </div>
        </div>
      )
    }
    
    return this.props.children
  }
}

// Usage
<TooltipErrorBoundary>
  <Tooltip content={<CustomTooltip />} />
</TooltipErrorBoundary>
```

### 🔄 Graceful Fallbacks

#### Simple Fallback Tooltip
```typescript
const FallbackTooltip = ({ active, payload, label }) => {
  if (!active || !payload?.length) return null
  
  return (
    <div className="bg-background border border-border corner-sm p-2 text-xs">
      <div className="font-medium">{label}</div>
      {payload.map((entry, index) => (
        <div key={index}>
          {entry.dataKey}: {entry.value}
        </div>
      ))}
    </div>
  )
}

// Use as fallback
<Tooltip 
  content={
    process.env.NODE_ENV === 'development' 
      ? <CustomTooltip chartType={chartType} monitorType={monitorType} />
      : <FallbackTooltip />
  }
/>
```

## Performance Issues

### 🐌 Slow Rendering

#### Optimize Data Processing
```typescript
// ✅ Memoize expensive calculations
const processedData = useMemo(() => {
  return data.map(item => ({
    ...item,
    // Pre-calculate formatted values
    formattedValue: formatValue(item.value, 'Mbps')
  }))
}, [data])

// ✅ Debounce rapid updates
const debouncedData = useMemo(() => 
  debounce(data, 100), 
  [data]
)
```

#### Reduce Re-renders
```typescript
// ✅ Stable tooltip configuration
const tooltipConfig = useMemo(() => 
  getTooltipConfig(chartType, monitorType), 
  [chartType, monitorType]
)

// ✅ Memoized custom tooltip
const MemoizedTooltip = memo(CustomTooltip)
```

---

**Related**: [Quick Reference](./quick-reference.md) | [Performance Guide](./performance-guide.md)  
**Language**: English | [中文](../zh/troubleshooting-guide.md)
