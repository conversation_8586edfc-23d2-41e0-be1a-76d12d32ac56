# Component API Reference

## Overview

Complete TypeScript interface documentation for the tooltip system components, including all props, configuration options, and type definitions.

## Core Interfaces

### CustomTooltipProps

The main interface for the custom tooltip component.

```typescript
interface CustomTooltipProps {
  active?: boolean
  payload?: any[]
  label?: string
  chartType?: ChartType
  monitorType?: 'network' | 'transaction'
}
```

#### Properties

| Property | Type | Required | Default | Description |
|----------|------|----------|---------|-------------|
| `active` | `boolean` | No | `false` | Whether the tooltip is currently active/visible |
| `payload` | `any[]` | No | `[]` | Array of data points from the chart at hover position |
| `label` | `string` | No | `undefined` | Time or category label for the data point |
| `chartType` | `ChartType` | No | `'line'` | Type of chart displaying the tooltip |
| `monitorType` | `'network' \| 'transaction'` | No | `'network'` | Monitor type for metric-specific formatting |

#### Usage Example

```typescript
function CustomTooltip({ active, payload, label, chartType, monitorType }: CustomTooltipProps) {
  if (!active || !payload || !payload.length) {
    return null
  }
  
  return (
    <div className="bg-background/95 backdrop-blur-sm border border-border corner-sm shadow-lg p-3">
      {/* Tooltip content */}
    </div>
  )
}
```

### ChartType

Enumeration of supported chart types.

```typescript
export type ChartType = 
  | 'area' 
  | 'line' 
  | 'bar' 
  | 'scatter' 
  | 'composed' 
  | 'step'
  | 'gradient-area' 
  | 'multi-line' 
  | 'stacked-bar' 
  | 'bubble' 
  | 'heatmap' 
  | 'radial'
  | 'waterfall' 
  | 'candlestick' 
  | 'pulse-wave'
```

#### Chart Type Descriptions

| Type | Description | Use Case | Cursor Style |
|------|-------------|----------|--------------|
| `area` | Area chart with gradient fill | Network traffic visualization | Dashed line |
| `line` | Simple line chart | Transaction trends | Dashed line |
| `bar` | Vertical bar chart | Discrete data points | Semi-transparent overlay |
| `scatter` | Scatter plot | Correlation analysis | Crosshair |
| `composed` | Combined chart types | Multi-metric displays | Neutral gray |
| `step` | Step-after line chart | Success rate tracking | Dashed line |
| `gradient-area` | Multi-color gradient area | Advanced visualizations | Dashed line |
| `multi-line` | Multiple line series | Comparative metrics | Dashed line |
| `stacked-bar` | Stacked bar chart | Resource usage breakdown | Semi-transparent overlay |
| `bubble` | Bubble chart | Multi-dimensional data | Crosshair |
| `heatmap` | Color-coded bars | Intensity visualization | Semi-transparent overlay |
| `radial` | Radial gradient area | Circular data patterns | Dashed line |
| `waterfall` | Sequential progression | Cumulative changes | Semi-transparent overlay |
| `candlestick` | Financial-style chart | OHLC data | Dashed line |
| `pulse-wave` | Enhanced composed chart | Real-time monitoring | Dashed line |

### TooltipConfig

Configuration object returned by `getTooltipConfig()` function.

```typescript
interface TooltipConfig {
  content: React.ReactElement<CustomTooltipProps>
  allowEscapeViewBox: { x: boolean; y: boolean }
  offset: number
  position: { x: undefined; y: undefined }
  wrapperStyle: {
    zIndex: number
    pointerEvents: string
  }
}
```

#### Properties

| Property | Type | Description |
|----------|------|-------------|
| `content` | `React.ReactElement` | Custom tooltip component instance |
| `allowEscapeViewBox` | `{ x: boolean; y: boolean }` | Allows tooltip to extend beyond chart boundaries |
| `offset` | `number` | Distance in pixels from hover point |
| `position` | `{ x: undefined; y: undefined }` | Forces automatic positioning |
| `wrapperStyle.zIndex` | `number` | Z-index for proper layering (9999) |
| `wrapperStyle.pointerEvents` | `string` | Prevents tooltip interaction ('none') |

### MetricColors

Interface for metric-specific color and label information.

```typescript
interface MetricColors {
  label: string
  unit: string
  color: string
}
```

#### Properties

| Property | Type | Description | Example |
|----------|------|-------------|---------|
| `label` | `string` | Human-readable metric name | "Inbound Traffic" |
| `unit` | `string` | Measurement unit | "Mbps" |
| `color` | `string` | CSS color value or custom property | "var(--chart-network-inbound)" |

#### Predefined Metrics

##### Network Metrics
```typescript
const networkMetrics = {
  inMbps: { label: 'Inbound Traffic', unit: 'Mbps', color: 'var(--chart-network-inbound)' },
  outMbps: { label: 'Outbound Traffic', unit: 'Mbps', color: 'var(--chart-network-outbound)' },
  rtt: { label: 'Round Trip Time', unit: 'ms', color: 'var(--chart-network-latency)' },
  loss: { label: 'Packet Loss', unit: '%', color: 'var(--chart-network-loss)' },
  retrans: { label: 'Retransmission', unit: '%', color: 'var(--chart-danger)' }
}
```

##### Transaction Metrics
```typescript
const transactionMetrics = {
  req: { label: 'Requests', unit: '/min', color: 'var(--chart-transaction-requests)' },
  successRate: { label: 'Success Rate', unit: '%', color: 'var(--chart-transaction-success)' },
  respP95: { label: 'Response Time P95', unit: 'ms', color: 'var(--chart-transaction-response)' },
  errorRate: { label: 'Error Rate', unit: '%', color: 'var(--chart-transaction-error)' }
}
```

## Utility Functions

### getTooltipConfig()

Creates standardized tooltip configuration for charts.

```typescript
function getTooltipConfig(
  chartType: ChartType, 
  monitorType: 'network' | 'transaction'
): TooltipConfig
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `chartType` | `ChartType` | Yes | Type of chart for cursor styling |
| `monitorType` | `'network' \| 'transaction'` | Yes | Monitor type for metric formatting |

#### Returns

`TooltipConfig` object with optimized settings for the specified chart and monitor type.

#### Example Usage

```typescript
import { getTooltipConfig } from '@/components/shared/MonitorCard'

// In chart component
<AreaChart data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
  <Area dataKey="inMbps" />
</AreaChart>
```

### getMetricInfo()

Retrieves metric metadata including label, unit, and color.

```typescript
function getMetricInfo(dataKey: string): MetricColors
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `dataKey` | `string` | Yes | Data key from chart payload |

#### Returns

`MetricColors` object with label, unit, and color information.

#### Example Usage

```typescript
import { getMetricInfo } from '@/lib/chart-colors'

const metricInfo = getMetricInfo('inMbps')
// Returns: { label: 'Inbound Traffic', unit: 'Mbps', color: 'var(--chart-network-inbound)' }
```

## CSS Classes

### Tooltip Styling Classes

Standard Tailwind CSS classes used for tooltip styling:

```typescript
const tooltipClasses = {
  container: 'bg-background/95 backdrop-blur-sm border border-border corner-sm shadow-lg p-3 min-w-[200px] max-w-[250px] z-50 relative',
  label: 'text-xs text-muted-foreground mb-2 font-medium',
  metricRow: 'flex items-center justify-between gap-3 py-1',
  colorIndicator: 'w-3 h-3 corner-xs flex-shrink-0',
  metricLabel: 'text-xs font-medium text-foreground flex-1',
  metricValue: 'text-xs font-mono text-foreground text-right'
}
```

### Design Token Integration

Tooltip styling uses CSS custom properties for theme consistency:

```css
/* Background and borders */
--background: /* Theme-aware background */
--border: /* Theme-aware border color */
--foreground: /* Theme-aware text color */
--muted-foreground: /* Secondary text color */

/* Chart-specific colors */
--chart-network-inbound: /* Network inbound traffic color */
--chart-network-outbound: /* Network outbound traffic color */
--chart-transaction-requests: /* Transaction request color */
--chart-transaction-success: /* Success rate color */
```

## Type Guards

### isValidChartType()

Type guard to validate chart type strings.

```typescript
function isValidChartType(type: string): type is ChartType {
  const validTypes: ChartType[] = [
    'area', 'line', 'bar', 'scatter', 'composed', 'step',
    'gradient-area', 'multi-line', 'stacked-bar', 'bubble', 
    'heatmap', 'radial', 'waterfall', 'candlestick', 'pulse-wave'
  ]
  return validTypes.includes(type as ChartType)
}
```

### isValidMonitorType()

Type guard to validate monitor type strings.

```typescript
function isValidMonitorType(type: string): type is 'network' | 'transaction' {
  return type === 'network' || type === 'transaction'
}
```

## Error Handling

### Graceful Degradation

The tooltip system includes built-in error handling:

```typescript
function CustomTooltip({ active, payload, label, chartType, monitorType }: CustomTooltipProps) {
  // Early return for invalid states
  if (!active || !payload || !payload.length) {
    return null
  }
  
  try {
    // Tooltip rendering logic
    return (
      <div className="tooltip-container">
        {/* Content */}
      </div>
    )
  } catch (error) {
    // Fallback to simple tooltip
    console.warn('Tooltip rendering error:', error)
    return (
      <div className="bg-background border border-border corner-sm p-2">
        <div className="text-xs text-foreground">
          {label || 'Data point'}
        </div>
      </div>
    )
  }
}
```

### Common Error Scenarios

1. **Missing Payload Data**: Returns null gracefully
2. **Invalid Chart Type**: Falls back to default line chart behavior
3. **Missing Metric Info**: Uses dataKey as label with empty unit
4. **Theme Token Missing**: Falls back to neutral colors

---

**Next**: [Implementation Guide](./implementation-guide.md) | [Back to Overview](./README.md)  
**Language**: English | [中文](../zh/component-api-reference.md)
