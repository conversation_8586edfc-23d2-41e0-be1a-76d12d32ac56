# Tooltip System Documentation

## Overview

Comprehensive bilingual documentation for the interactive tooltip system used in chart data visualization within the Cape Horn Monitor project. This system provides advanced tooltip functionality for MonitorCard components with smart positioning, design system integration, and cross-browser compatibility.

## 🌐 Language Selection

Choose your preferred language for documentation:

### English Documentation
- [**📖 Complete Guide**](./en/README.md) - Full English documentation
- [**⚡ Quick Start**](./en/quick-reference.md) - Common tasks and code snippets
- [**🔧 API Reference**](./en/component-api-reference.md) - Complete TypeScript interfaces
- [**📋 Implementation**](./en/implementation-guide.md) - Step-by-step setup guide

### 中文文档
- [**📖 完整指南**](./zh/README.md) - 完整中文文档
- [**⚡ 快速参考**](./zh/quick-reference.md) - 常见任务和代码片段
- [**🔧 API参考**](./zh/component-api-reference.md) - 完整TypeScript接口
- [**📋 实现指南**](./zh/implementation-guide.md) - 分步设置指南

## 🚀 Quick Start

### Basic Implementation
```typescript
import { Tooltip } from 'recharts'
import { getTooltipConfig } from '@/components/shared/MonitorCard'

<AreaChart data={data}>
  <Tooltip {...getTooltipConfig('area', 'network')} />
  <Area dataKey="inMbps" stroke="var(--chart-network-inbound)" />
</AreaChart>
```

### Custom Tooltip
```typescript
import { CustomTooltip } from '@/components/shared/MonitorCard'

<Tooltip 
  content={<CustomTooltip chartType="line" monitorType="transaction" />}
  allowEscapeViewBox={{ x: true, y: true }}
  offset={15}
/>
```

## 📊 Key Features

### 🎯 Smart Positioning
- **Escape ViewBox**: Tooltips extend beyond chart boundaries
- **Collision Detection**: Automatic positioning to avoid content blocking
- **Z-Index Management**: Proper layering with other UI elements
- **Offset Control**: Configurable spacing from hover point

### 🎨 Design System Integration
- **CSS Custom Properties**: Uses design tokens for theming
- **Theme Awareness**: Automatic light/dark mode adaptation
- **Color Coordination**: Metric colors match chart elements
- **Typography Consistency**: Follows established text hierarchy

### 📈 Chart Type Support
- **15+ Chart Types**: Area, line, bar, scatter, composed, and more
- **Metric-Specific**: Different tooltips for network vs transaction data
- **Dynamic Content**: Adapts to available data and chart configuration
- **Interactive Cursors**: Chart-specific cursor styling

## 🏗️ Architecture

### Component Structure
```
MonitorCard
├── Chart Components (Area, Line, Bar, etc.)
│   └── Tooltip
│       └── CustomTooltip
│           ├── Metric Labels
│           ├── Color Indicators  
│           ├── Formatted Values
│           └── Units
└── Tooltip Configuration
    ├── Positioning Logic
    ├── Theme Integration
    └── Cursor Styling
```

### Data Flow
```
Chart Data → Metric Mapping → Color Assignment → Tooltip Rendering
     ↓              ↓              ↓              ↓
Monitor Type → getMetricInfo() → Design Tokens → CustomTooltip
```

## 📚 Documentation Structure

### Core Documentation
| Document | English | 中文 | Description |
|----------|---------|------|-------------|
| **Main Guide** | [README.md](./en/README.md) | [README.md](./zh/README.md) | Complete overview and getting started |
| **API Reference** | [component-api-reference.md](./en/component-api-reference.md) | [component-api-reference.md](./zh/component-api-reference.md) | TypeScript interfaces and props |
| **Implementation** | [implementation-guide.md](./en/implementation-guide.md) | [implementation-guide.md](./zh/implementation-guide.md) | Step-by-step setup instructions |
| **Quick Reference** | [quick-reference.md](./en/quick-reference.md) | [quick-reference.md](./zh/quick-reference.md) | Common tasks and code snippets |

### Developer Resources
| Document | English | 中文 | Description |
|----------|---------|------|-------------|
| **Troubleshooting** | [troubleshooting-guide.md](./en/troubleshooting-guide.md) | [troubleshooting-guide.md](./zh/troubleshooting-guide.md) | Common issues and solutions |
| **Performance** | [performance-guide.md](./en/performance-guide.md) | [performance-guide.md](./zh/performance-guide.md) | Optimization best practices |
| **Testing** | [testing-guide.md](./en/testing-guide.md) | [testing-guide.md](./zh/testing-guide.md) | Testing approaches and requirements |
| **Integration** | [integration-patterns.md](./en/integration-patterns.md) | [integration-patterns.md](./zh/integration-patterns.md) | System integration patterns |

### Quality Assurance
| Document | English | 中文 | Description |
|----------|---------|------|-------------|
| **Accessibility** | [accessibility-guide.md](./en/accessibility-guide.md) | [accessibility-guide.md](./zh/accessibility-guide.md) | WCAG compliance and best practices |
| **Cross-Browser** | [cross-browser-compatibility.md](./en/cross-browser-compatibility.md) | [cross-browser-compatibility.md](./zh/cross-browser-compatibility.md) | Browser support and testing |
| **Mobile** | [mobile-responsiveness.md](./en/mobile-responsiveness.md) | [mobile-responsiveness.md](./zh/mobile-responsiveness.md) | Touch and mobile considerations |
| **Validation** | [validation-checklist.md](./en/validation-checklist.md) | [validation-checklist.md](./zh/validation-checklist.md) | Implementation validation checklist |

## 🌐 Browser Support

| Browser | Version | Support Level | Notes |
|---------|---------|---------------|-------|
| Chrome | 90+ | ✅ Full | Recommended for development |
| Firefox | 88+ | ✅ Full | All features supported |
| Safari | 14+ | ✅ Full | Webkit-specific optimizations |
| Edge | 90+ | ✅ Full | Chromium-based versions |
| Mobile Safari | 14+ | ✅ Full | Touch-optimized interactions |
| Chrome Mobile | 90+ | ✅ Full | Responsive design tested |

## ⚡ Performance

### Bundle Impact
- **Core Tooltip**: ~2KB gzipped
- **Chart Integration**: ~1KB additional per chart type
- **Design Token System**: ~0.5KB shared across components

### Runtime Performance
- **Rendering**: <16ms tooltip display time
- **Memory**: <1MB additional heap usage
- **CPU**: Minimal impact on chart interactions

## 🛠️ Development Workflow

### Getting Started
1. Choose your language: [English](./en/README.md) | [中文](./zh/README.md)
2. Read the [API Reference](./en/component-api-reference.md) for interfaces
3. Follow the [Implementation Guide](./en/implementation-guide.md) for setup
4. Use [Quick Reference](./en/quick-reference.md) for daily tasks

### Quality Assurance
1. Follow the [Validation Checklist](./en/validation-checklist.md)
2. Test with [Testing Guide](./en/testing-guide.md) approaches
3. Verify [Accessibility](./en/accessibility-guide.md) compliance
4. Check [Cross-Browser](./en/cross-browser-compatibility.md) compatibility

### Troubleshooting
1. Check [Troubleshooting Guide](./en/troubleshooting-guide.md) for common issues
2. Review [Performance Guide](./en/performance-guide.md) for optimization
3. Consult [Integration Patterns](./en/integration-patterns.md) for system integration

## 🤝 Contributing

### Documentation Updates
- Maintain both English and Chinese versions
- Follow established template structure
- Test all code examples before committing
- Update cross-references when adding new sections

### Code Contributions
- Maintain TypeScript interfaces
- Follow accessibility guidelines
- Include comprehensive tests
- Document breaking changes in both languages

## 📞 Support

### Internal Resources
- [Design System Documentation](../README.md)
- [MonitorCard Component Guide](../components/monitor-card-system.md)
- [Chart Color System](../standards/chart-color-system.md)

### External Resources
- [Recharts Documentation](https://recharts.org/en-US/)
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [CSS Custom Properties](https://developer.mozilla.org/en-US/docs/Web/CSS/--*)

---

**Languages**: English | 中文  
**Last Updated**: August 2024  
**Version**: 3.0 (Design Token Integration)  
**Maintained By**: Frontend Development Team
