# Tooltip Demo Page Documentation

## Overview

The Tooltip Demo Page (`/tooltip-demo`) is a comprehensive demonstration of the interactive tooltip system used in MonitorCard components. It showcases various chart types with their corresponding tooltip behaviors and provides detailed documentation for developers.

## Page Location

- **Path**: `/app/(dev)/tooltip-demo/page.tsx`
- **Access URL**: `http://localhost:3000/tooltip-demo`
- **Navigation**: Sidebar Activity icon or Home page Development Tools section

## Features

### 1. Interactive Chart Examples

The page displays 6 different MonitorCard components, each showcasing a different chart type:

#### Chart Types Demonstrated
1. **Area Chart** - Network traffic with gradient fill
2. **Line Chart** - Transaction volume with spike pattern
3. **Stacked Bar Chart** - Resource usage with step data
4. **Multi-line Chart** - Performance metrics with oscillating pattern
5. **Bubble Chart** - Correlation analysis with random walk data
6. **Gradient Area Chart** - Advanced gradient flow with sawtooth pattern

#### Data Patterns
- **Normal**: Standard data distribution
- **Spike**: Sudden peak in the middle
- **Step**: Step function with level changes
- **Oscillating**: Sine wave pattern
- **Random-walk**: Random noise pattern
- **Sawtooth**: Triangular wave pattern

### 2. Design System Integration

The page demonstrates proper use of:
- **Design Tokens**: No hardcoded colors, uses CSS custom properties
- **Icon System**: Intelligent icon selection based on monitor type
- **Typography**: Consistent text hierarchy and spacing
- **Layout**: Responsive grid system with proper spacing

### 3. Educational Content

#### Interactive Instructions
- Step-by-step guide for testing tooltips
- Visual indicators and numbered steps
- Clear explanations of expected behaviors

#### Feature Documentation
- **Visual Elements**: Color coding, backgrounds, typography
- **Data Display**: Metric names, units, formatting
- **Technical Implementation**: Chart integration, performance notes
- **Usage Guidelines**: Do's, don'ts, and best practices

## Technical Implementation

### Component Structure
```typescript
// Modern implementation using design tokens
const demoMonitors: MonitorCardData[] = [
  {
    id: "demo-1",
    name: "Network Traffic Monitor",
    type: "network",
    showMetrics: true,
    chartType: "area",
    iconType: "network",
    dataPattern: "normal",
    // No hardcoded chartColors - uses design system
  }
]
```

### Key Improvements Made
1. **Removed Hardcoded Colors**: Replaced with design token system
2. **Added Proper Navigation**: Integrated with Sidebar and Breadcrumb
3. **Enhanced Layout**: Full-page layout with proper header and sections
4. **Improved Documentation**: Comprehensive feature explanations
5. **Better UX**: Clear instructions and visual hierarchy

### Dependencies
```typescript
import MonitorCard, { MonitorCardData } from '@/components/shared/MonitorCard'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Sidebar from '@/components/shared/Sidebar'
import Breadcrumb from '@/components/shared/Breadcrumb'
```

## Tooltip System Features

### Visual Design
- **Color-coded indicators**: Match chart colors automatically
- **Semi-transparent background**: Subtle backdrop with border
- **Consistent typography**: Follows design system fonts and sizes
- **Smooth animations**: Responsive positioning and transitions
- **Theme awareness**: Adapts to light/dark mode

### Data Presentation
- **Human-readable names**: Clear metric descriptions
- **Proper units**: Mbps, ms, %, /min with correct formatting
- **Formatted values**: Appropriate decimal precision
- **Time labels**: Consistent time formatting
- **Health context**: NHI/THI indicators when available

### Technical Features
- **Recharts integration**: Built on robust charting library
- **Custom components**: Design system compliant tooltips
- **Automatic detection**: Smart metric identification
- **Chart-specific behavior**: Different tooltips for different chart types
- **Performance optimized**: Memoization and efficient rendering

## Usage Guidelines

### Best Practices
✅ **Do:**
- Use consistent color coding across charts
- Include appropriate units for all metrics
- Format numbers for optimal readability
- Provide meaningful metric names and descriptions
- Test tooltip behavior across all chart types

❌ **Don't:**
- Overcrowd tooltips with excessive information
- Use inconsistent formatting between similar metrics
- Ignore accessibility requirements (contrast, keyboard navigation)
- Hardcode colors or styling values
- Forget to test mobile responsiveness

💡 **Tips:**
- Test with realistic data ranges and edge cases
- Consider tooltip positioning on different screen sizes
- Validate color contrast ratios for accessibility
- Use semantic HTML structure for screen readers
- Document any custom tooltip implementations

## Development Notes

### File Organization
- **Main Component**: `/app/(dev)/tooltip-demo/page.tsx`
- **Sidebar Integration**: Updated to include tooltip demo navigation
- **Documentation**: This file provides comprehensive reference

### Testing Considerations
- **Interactive Testing**: Hover over each chart type
- **Responsive Testing**: Verify behavior on different screen sizes
- **Accessibility Testing**: Keyboard navigation and screen reader support
- **Cross-browser Testing**: Ensure consistent behavior across browsers

### Future Enhancements
- Add more chart type examples as they're developed
- Include accessibility testing tools integration
- Add performance metrics display
- Consider adding tooltip customization examples

## Related Documentation

- [MonitorCard Component](./monitor-card-tooltip-implementation.md)
- [Chart Color System](./chart-color-system.md)
- [Frontend Guidelines](../frontend-guidelines/README.md)
- [Design System Documentation](./design-system-overview.md)

---

**Last Updated**: August 2024  
**Component Version**: v3.0 (Design Token Integration)  
**Status**: ✅ Active Development Tool
